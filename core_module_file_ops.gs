/*
 * 檔案: core_module_file_ops.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組檔案操作功能
 * 依賴: []
 * 最後更新: 2025-07-11
 */

// == 模組檔案操作功能 ==
// 提供檔案移動、檢查等基礎操作

/**
 * 📁 將檔案移動到根目錄
 */
function moveFileToRoot(fileName) {
  try {
    // 檢查檔案是否在 _sleeping 資料夾
    const inSleeping = checkFileExists(fileName, '_sleeping');
    const inRoot = checkFileExists(fileName, 'root');
    
    if (inRoot && !inSleeping) {
      return { success: true, message: '檔案已在根目錄', action: 'none' };
    }
    
    if (!inSleeping) {
      return { success: false, message: '檔案不存在於 _sleeping 資料夾', action: 'none' };
    }
    
    // 模擬檔案移動（實際上 Google Apps Script 無法真正移動檔案）
    console.log(`📁 模擬移動檔案: ${fileName} 從 _sleeping 到根目錄`);
    
    // 使用 PropertiesService 記錄檔案狀態
    const properties = PropertiesService.getScriptProperties();
    properties.setProperty(`file_location_${fileName}`, 'root');
    
    return { 
      success: true, 
      message: '檔案已移動到根目錄', 
      action: 'moved',
      note: '使用 PropertiesService 模擬檔案移動'
    };
    
  } catch (error) {
    console.error(`❌ 移動檔案到根目錄失敗: ${fileName}`, error);
    return { success: false, message: error.message, action: 'error' };
  }
}

/**
 * 📁 將檔案移動到 _sleeping 資料夾
 */
function moveFileToSleeping(fileName) {
  try {
    // 檢查檔案是否在根目錄
    const inRoot = checkFileExists(fileName, 'root');
    const inSleeping = checkFileExists(fileName, '_sleeping');
    
    if (inSleeping && !inRoot) {
      return { success: true, message: '檔案已在 _sleeping 資料夾', action: 'none' };
    }
    
    if (!inRoot) {
      return { success: false, message: '檔案不存在於根目錄', action: 'none' };
    }
    
    // 模擬檔案移動
    console.log(`📁 模擬移動檔案: ${fileName} 從根目錄到 _sleeping`);
    
    // 使用 PropertiesService 記錄檔案狀態
    const properties = PropertiesService.getScriptProperties();
    properties.setProperty(`file_location_${fileName}`, '_sleeping');
    
    return { 
      success: true, 
      message: '檔案已移動到 _sleeping 資料夾', 
      action: 'moved',
      note: '使用 PropertiesService 模擬檔案移動'
    };
    
  } catch (error) {
    console.error(`❌ 移動檔案到 _sleeping 失敗: ${fileName}`, error);
    return { success: false, message: error.message, action: 'error' };
  }
}

/**
 * 🔍 檢查檔案是否存在於指定位置
 */
function checkFileExists(fileName, location) {
  try {
    // 在 Google Apps Script 環境中，我們使用 PropertiesService 來模擬檔案位置
    const properties = PropertiesService.getScriptProperties();
    const fileLocation = properties.getProperty(`file_location_${fileName}`);
    
    // 如果沒有記錄，假設檔案在根目錄
    if (!fileLocation) {
      return location === 'root';
    }
    
    return fileLocation === location;
    
  } catch (error) {
    console.error(`❌ 檢查檔案存在性失敗: ${fileName}`, error);
    return false;
  }
}

/**
 * 📊 獲取檔案位置資訊
 */
function getFileLocation(fileName) {
  try {
    const properties = PropertiesService.getScriptProperties();
    const location = properties.getProperty(`file_location_${fileName}`);
    
    return {
      fileName: fileName,
      location: location || 'root',
      exists: true,
      note: location ? '已記錄位置' : '預設位置（根目錄）'
    };
    
  } catch (error) {
    console.error(`❌ 獲取檔案位置失敗: ${fileName}`, error);
    return {
      fileName: fileName,
      location: 'unknown',
      exists: false,
      error: error.message
    };
  }
}

/**
 * 📋 獲取所有檔案位置狀態
 */
function getAllFileLocations() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    const fileLocations = {};
    
    // 篩選出檔案位置相關的屬性
    for (const [key, value] of Object.entries(allProperties)) {
      if (key.startsWith('file_location_')) {
        const fileName = key.replace('file_location_', '');
        fileLocations[fileName] = value;
      }
    }
    
    return {
      success: true,
      fileLocations: fileLocations,
      totalFiles: Object.keys(fileLocations).length
    };
    
  } catch (error) {
    console.error('❌ 獲取所有檔案位置失敗:', error);
    return {
      success: false,
      error: error.message,
      fileLocations: {},
      totalFiles: 0
    };
  }
}

/**
 * 🧹 清理檔案位置記錄
 */
function cleanupFileLocationRecords() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    let deletedCount = 0;
    
    // 刪除所有檔案位置記錄
    for (const key of Object.keys(allProperties)) {
      if (key.startsWith('file_location_')) {
        properties.deleteProperty(key);
        deletedCount++;
      }
    }
    
    console.log(`🧹 已清理 ${deletedCount} 個檔案位置記錄`);
    
    return {
      success: true,
      message: `已清理 ${deletedCount} 個檔案位置記錄`,
      deletedCount: deletedCount
    };
    
  } catch (error) {
    console.error('❌ 清理檔案位置記錄失敗:', error);
    return {
      success: false,
      error: error.message,
      deletedCount: 0
    };
  }
}

/**
 * 🔄 重置檔案位置（全部設為根目錄）
 */
function resetAllFilesToRoot() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const allProperties = properties.getProperties();
    let resetCount = 0;
    
    // 將所有檔案位置重置為根目錄
    for (const key of Object.keys(allProperties)) {
      if (key.startsWith('file_location_')) {
        properties.setProperty(key, 'root');
        resetCount++;
      }
    }
    
    console.log(`🔄 已重置 ${resetCount} 個檔案位置到根目錄`);
    
    return {
      success: true,
      message: `已重置 ${resetCount} 個檔案位置到根目錄`,
      resetCount: resetCount
    };
    
  } catch (error) {
    console.error('❌ 重置檔案位置失敗:', error);
    return {
      success: false,
      error: error.message,
      resetCount: 0
    };
  }
}

/**
 * 📊 檔案位置統計
 */
function getFileLocationStatistics() {
  try {
    const allLocations = getAllFileLocations();
    
    if (!allLocations.success) {
      return allLocations;
    }
    
    const stats = {
      total: 0,
      inRoot: 0,
      inSleeping: 0,
      unknown: 0,
      byLocation: {}
    };
    
    for (const [fileName, location] of Object.entries(allLocations.fileLocations)) {
      stats.total++;
      
      if (location === 'root') {
        stats.inRoot++;
      } else if (location === '_sleeping') {
        stats.inSleeping++;
      } else {
        stats.unknown++;
      }
      
      // 按位置分組統計
      if (!stats.byLocation[location]) {
        stats.byLocation[location] = [];
      }
      stats.byLocation[location].push(fileName);
    }
    
    return {
      success: true,
      statistics: stats
    };
    
  } catch (error) {
    console.error('❌ 獲取檔案位置統計失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}
