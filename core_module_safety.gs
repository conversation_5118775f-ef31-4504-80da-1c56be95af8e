/*
 * 檔案: core_module_safety.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組安全網檢查和自動發現功能
 * 依賴: [core_module_mapping.gs, core_module_file_ops.gs]
 * 最後更新: 2025-07-11
 */

// == 模組安全網檢查和自動發現功能 ==
// 提供模組完整性檢查和自動發現新模組的功能

/**
 * 🛡️ 執行安全網檢查
 * 檢查模組映射表與實際檔案的一致性
 */
function runSafetyNetCheck() {
  try {
    console.log('🛡️ 開始執行安全網檢查...');
    
    const results = {
      timestamp: new Date(),
      mappingValidation: validateModuleMapping(),
      fileConsistency: checkFileConsistency(),
      missingFiles: findMissingFiles(),
      orphanedFiles: findOrphanedFiles(),
      recommendations: []
    };
    
    // 生成建議
    results.recommendations = generateRecommendations(results);
    
    console.log('✅ 安全網檢查完成');
    return results;
    
  } catch (error) {
    console.error('❌ 安全網檢查失敗:', error);
    return {
      timestamp: new Date(),
      error: error.message,
      success: false
    };
  }
}

/**
 * 🔍 檢查檔案一致性
 * 比較映射表中的檔案與實際存在的檔案
 */
function checkFileConsistency() {
  const consistency = {
    totalMappedFiles: 0,
    existingFiles: 0,
    missingFiles: [],
    inconsistentLocations: [],
    summary: ''
  };
  
  try {
    const allLocations = getAllFileLocations();
    
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
        continue;
      }
      
      for (const fileName of moduleInfo.files) {
        consistency.totalMappedFiles++;
        
        const fileLocation = getFileLocation(fileName);
        
        if (fileLocation.exists) {
          consistency.existingFiles++;
          
          // 檢查檔案位置是否與功能狀態一致
          const featureEnabled = isFeatureEnabled(featureName);
          const expectedLocation = featureEnabled ? 'root' : '_sleeping';
          
          if (fileLocation.location !== expectedLocation) {
            consistency.inconsistentLocations.push({
              fileName: fileName,
              feature: featureName,
              currentLocation: fileLocation.location,
              expectedLocation: expectedLocation,
              featureEnabled: featureEnabled
            });
          }
        } else {
          consistency.missingFiles.push({
            fileName: fileName,
            feature: featureName
          });
        }
      }
    }
    
    // 生成摘要
    const missingCount = consistency.missingFiles.length;
    const inconsistentCount = consistency.inconsistentLocations.length;
    
    if (missingCount === 0 && inconsistentCount === 0) {
      consistency.summary = '✅ 所有檔案位置一致';
    } else {
      consistency.summary = `⚠️ 發現 ${missingCount} 個缺失檔案，${inconsistentCount} 個位置不一致`;
    }
    
  } catch (error) {
    console.error('❌ 檢查檔案一致性失敗:', error);
    consistency.error = error.message;
  }
  
  return consistency;
}

/**
 * 🔍 查找缺失的檔案
 */
function findMissingFiles() {
  const missing = [];
  
  try {
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
        continue;
      }
      
      for (const fileName of moduleInfo.files) {
        const fileLocation = getFileLocation(fileName);
        
        if (!fileLocation.exists) {
          missing.push({
            fileName: fileName,
            feature: featureName,
            description: moduleInfo.description,
            prefix: moduleInfo.prefix
          });
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 查找缺失檔案失敗:', error);
  }
  
  return missing;
}

/**
 * 🔍 查找孤立的檔案
 * 找出存在但不在映射表中的檔案
 */
function findOrphanedFiles() {
  const orphaned = [];
  
  try {
    // 獲取所有已記錄的檔案
    const allLocations = getAllFileLocations();
    
    if (!allLocations.success) {
      return orphaned;
    }
    
    // 收集映射表中的所有檔案
    const mappedFiles = new Set();
    for (const moduleInfo of Object.values(MODULE_FILE_MAPPING)) {
      if (moduleInfo.files && Array.isArray(moduleInfo.files)) {
        moduleInfo.files.forEach(file => mappedFiles.add(file));
      }
    }
    
    // 找出不在映射表中的檔案
    for (const fileName of Object.keys(allLocations.fileLocations)) {
      if (!mappedFiles.has(fileName)) {
        orphaned.push({
          fileName: fileName,
          location: allLocations.fileLocations[fileName],
          suggestion: suggestFeatureForFile(fileName)
        });
      }
    }
    
  } catch (error) {
    console.error('❌ 查找孤立檔案失敗:', error);
  }
  
  return orphaned;
}

/**
 * 🤖 為檔案建議對應的功能
 */
function suggestFeatureForFile(fileName) {
  // 根據檔案名稱模式建議功能
  const patterns = {
    'modules_image': 'IMAGE_GENERATION',
    'modules_audio': 'TEXT_TO_SPEECH',
    'modules_note': 'NOTE_TAKING',
    'modules_file': 'FILE_QUERY',
    'modules_group': 'GROUP_MEMBER_QUERY',
    'modules_social': 'SOCIAL_MEDIA_POST',
    'modules_knowledge': 'KNOWLEDGE_BASE',
    'modules_line_webhook': 'LINE_WEBHOOK',
    'modules_ai_prompts': 'AI_PROMPTS'
  };
  
  for (const [pattern, feature] of Object.entries(patterns)) {
    if (fileName.startsWith(pattern)) {
      return feature;
    }
  }
  
  return 'UNKNOWN';
}

/**
 * 💡 生成建議
 */
function generateRecommendations(checkResults) {
  const recommendations = [];
  
  try {
    // 檢查映射表驗證結果
    if (!checkResults.mappingValidation.isValid) {
      recommendations.push({
        type: 'error',
        priority: 'high',
        message: '映射表驗證失敗，需要修復映射表定義',
        action: '檢查並修復 MODULE_FILE_MAPPING 中的錯誤'
      });
    }
    
    // 檢查缺失檔案
    if (checkResults.missingFiles.length > 0) {
      recommendations.push({
        type: 'warning',
        priority: 'medium',
        message: `發現 ${checkResults.missingFiles.length} 個缺失檔案`,
        action: '檢查檔案是否已被刪除或重命名，更新映射表或恢復檔案'
      });
    }
    
    // 檢查孤立檔案
    if (checkResults.orphanedFiles.length > 0) {
      recommendations.push({
        type: 'info',
        priority: 'low',
        message: `發現 ${checkResults.orphanedFiles.length} 個孤立檔案`,
        action: '將孤立檔案加入對應的功能映射表，或移除不需要的檔案'
      });
    }
    
    // 檢查位置不一致
    if (checkResults.fileConsistency.inconsistentLocations.length > 0) {
      recommendations.push({
        type: 'warning',
        priority: 'medium',
        message: `發現 ${checkResults.fileConsistency.inconsistentLocations.length} 個檔案位置不一致`,
        action: '執行 autoManageModules() 來同步檔案位置與功能開關狀態'
      });
    }
    
    // 如果一切正常
    if (recommendations.length === 0) {
      recommendations.push({
        type: 'success',
        priority: 'info',
        message: '所有檢查通過，模組系統運行正常',
        action: '無需採取行動'
      });
    }
    
  } catch (error) {
    console.error('❌ 生成建議失敗:', error);
    recommendations.push({
      type: 'error',
      priority: 'high',
      message: '生成建議時發生錯誤',
      action: `檢查錯誤：${error.message}`
    });
  }
  
  return recommendations;
}

/**
 * 🔧 自動修復檔案位置不一致
 */
function autoFixInconsistentLocations() {
  try {
    console.log('🔧 開始自動修復檔案位置不一致...');
    
    const checkResults = runSafetyNetCheck();
    const inconsistentFiles = checkResults.fileConsistency.inconsistentLocations;
    
    if (inconsistentFiles.length === 0) {
      return {
        success: true,
        message: '沒有發現位置不一致的檔案',
        fixedCount: 0
      };
    }
    
    let fixedCount = 0;
    const errors = [];
    
    for (const fileInfo of inconsistentFiles) {
      try {
        const moveResult = fileInfo.expectedLocation === 'root' 
          ? moveFileToRoot(fileInfo.fileName)
          : moveFileToSleeping(fileInfo.fileName);
          
        if (moveResult.success) {
          fixedCount++;
          console.log(`✅ 已修復: ${fileInfo.fileName} → ${fileInfo.expectedLocation}`);
        } else {
          errors.push(`${fileInfo.fileName}: ${moveResult.message}`);
        }
        
      } catch (error) {
        errors.push(`${fileInfo.fileName}: ${error.message}`);
      }
    }
    
    return {
      success: errors.length === 0,
      message: `已修復 ${fixedCount} 個檔案位置，${errors.length} 個失敗`,
      fixedCount: fixedCount,
      errors: errors
    };
    
  } catch (error) {
    console.error('❌ 自動修復失敗:', error);
    return {
      success: false,
      message: error.message,
      fixedCount: 0
    };
  }
}
