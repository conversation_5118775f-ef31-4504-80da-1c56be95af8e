/*
 * 檔案: core_utils_reply.gs
 * 分類: core
 * 功能開關: -
 * 描述: 複雜回覆功能（音頻、圖片、Flex Message）
 * 依賴: [core_utils_config.gs, core_utils_helpers.gs]
 * 最後更新: 2025-07-11
 */

// == 複雜回覆功能模組 ==
// 提供音頻回覆、圖片回覆、Flex Message 等複雜回覆功能

/**
 * 🎵 AI-First 音頻檔案回覆（修復播放按鈕版本）
 * 智能決定是發送實際音頻檔案還是包含連結的文字訊息
 * 🔧 v2.1 - 修復播放按鈕被淹沒問題：成功時只發送音頻，失敗時才發送文字說明
 */
function replyWithAudio(replyToken, audioResult, originalText) {
  try {
    const startTime = Date.now(); // 🕐 開始計時
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    console.log(`🔊 嘗試發送音頻回覆，可播放: ${audioResult.isPlayable}`);

    // 🤖 AI 決定回覆策略：優先使用 Cloudinary M4A 格式
    const messages = [];
    let audioSent = false; // 🔧 新增：追蹤音頻是否成功發送

    // 🎯 嘗試發送可播放的 M4A 音頻檔案
    if (audioResult.isPlayable && audioResult.cloudinaryUrl) {
      try {
        console.log(`✅ 使用 Cloudinary M4A 音頻: ${audioResult.cloudinaryUrl}`);
        
        // 估算音頻時長（基於文字長度，實際應該從 TTS API 獲取）
        const estimatedDuration = Math.min(Math.max(originalText.length * 150, 1000), 300000); // 1秒到5分鐘
        
        // 添加可播放的 M4A 音頻訊息
        messages.push({
          type: 'audio',
          originalContentUrl: audioResult.cloudinaryUrl,
          duration: estimatedDuration
        });

        audioSent = true; // 🔧 標記音頻發送成功
        console.log(`🎵 音頻訊息已準備，時長: ${estimatedDuration}ms`);

      } catch (audioError) {
        console.log('Cloudinary 音頻發送失敗，改為文字回覆:', audioError);
        audioSent = false; // 🔧 標記音頻發送失敗
      }
    } else {
      console.log('⚠️ 沒有可播放的音頻 URL，將發送文字回覆');
      audioSent = false; // 🔧 標記音頻發送失敗
    }

    // 🎯 修復：只有在音頻發送失敗時才添加文字說明
    if (!audioSent) {
      const smartResponse = generateAudioFallbackText(originalText, audioResult);
      messages.push({
        type: 'text',
        text: smartResponse
      });
      console.log('📝 音頻發送失敗，添加文字說明');
    } else {
      console.log('🎵 音頻發送成功，保持播放按鈕乾淨顯示（不添加文字訊息）');
    }

    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: messages
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }

    console.log(`✅ 音頻回覆成功發送，包含 ${messages.length} 個訊息`);
    const responseTime = Date.now() - startTime; // 🕐 計算回應時間
    logActivity('Reply', '音頻回覆成功', 'Success', 'audio', 'replyWithAudio', `可播放: ${audioResult.isPlayable}, 訊息數: ${messages.length}, 純音頻: ${audioSent}`, responseTime);

  } catch (error) {
    console.error('音頻回覆錯誤:', error);
    // 備用：發送純文字回覆
    const fallbackText = generateAudioFallbackText(originalText, audioResult);
    replyMessage(replyToken, fallbackText);
  }
}

// 6. 🎙️ 對話音頻專用：調用 Native Audio Dialog 模型
function callGeminiAudioDialog(text, conversationContext = {}) {
  const config = getConfig();
  if (!config.geminiApiKey) throw new Error('Gemini API Key 未設定');

  const modelName = config.audioDialogModel;
  console.log(`🎙️ 使用對話音頻模型: ${modelName}`);

  try {
    // 🎯 使用 Native Audio Dialog 模型
    const modelConfig = getModelConfig(modelName);
    const apiVersion = modelConfig.apiVersion;
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;

    // 構建對話上下文
    let contextPrompt = text;
    if (conversationContext.conversationHistory && conversationContext.conversationHistory.length > 0) {
      const historyText = conversationContext.conversationHistory.map(h => `用戶: ${h.user}\n助理: ${h.assistant}`).join('\n');
      contextPrompt = `對話歷史:\n${historyText}\n\n當前用戶: ${text}`;
    }

    // Native Audio Dialog 配置
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: contextPrompt }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: 'Kore' // 可以根據需要調整語音
            }
          }
        }
      }
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });

    const responseCode = response.getResponseCode();
    const responseText = response.getContentText();

    if (responseCode !== 200) {
      console.error(`Native Audio Dialog API 錯誤 - Code: ${responseCode}, Response: ${responseText}`);
      return {
        success: false,
        error: `API 調用失敗 (${responseCode})`,
        modelUsed: modelName
      };
    }

    const result = JSON.parse(responseText);

    // 檢查是否有音頻回應
    if (result.candidates && result.candidates[0] && result.candidates[0].content) {
      const candidate = result.candidates[0];

      // 查找音頻數據
      let audioData = null;
      if (candidate.content.parts) {
        for (const part of candidate.content.parts) {
          if (part.inlineData && part.inlineData.mimeType && part.inlineData.mimeType.startsWith('audio/')) {
            audioData = part;
            break;
          }
        }
      }

      if (audioData) {
        console.log(`🎙️ Native Audio Dialog 成功生成音頻`);

        // 處理音頻數據（類似 TTS 處理）
        const rawAudioBytes = Utilities.base64Decode(audioData.inlineData.data);
        const audioParams = parseAudioMimeType(audioData.inlineData.mimeType);
        const wavBlob = createWAVFromPCM(rawAudioBytes, audioParams);

        // 上傳到 Drive 和 Cloudinary
        const fileName = `native_audio_dialog_${Date.now()}.wav`;
        const driveFile = DriveApp.createFile(wavBlob.setName(fileName));
        const driveUrl = driveFile.getUrl();

        // 嘗試上傳到 Cloudinary（使用現有的上傳函數）
        let cloudinaryUrl = null;
        let cloudinaryError = null;
        try {
          cloudinaryUrl = uploadAudioToCloudinary(wavBlob, originalMessage);
        } catch (error) {
          cloudinaryError = error.message;
          console.warn('Cloudinary 上傳失敗:', error);
        }

        return {
          success: true,
          fileName: fileName,
          driveUrl: driveUrl,
          cloudinaryUrl: cloudinaryUrl,
          cloudinaryError: cloudinaryError,
          isPlayable: !!cloudinaryUrl,
          modelUsed: modelName,
          mode: 'native_audio_dialog'
        };
      } else {
        // 沒有音頻數據，可能只有文字回應
        const textResponse = candidate.content.parts.find(p => p.text)?.text || '無回應';
        return {
          success: false,
          error: 'Native Audio Dialog 沒有生成音頻，只有文字回應',
          textResponse: textResponse,
          modelUsed: modelName
        };
      }
    } else {
      return {
        success: false,
        error: 'Native Audio Dialog 回應格式不正確',
        modelUsed: modelName
      };
    }

  } catch (error) {
    console.error('Native Audio Dialog 調用失敗:', error);
    return {
      success: false,
      error: error.message,
      modelUsed: modelName
    };
  }
}

/**
 * 🌟 Flex Message 圖片回覆（v4.1 - 互動按鈕版）
 * 🎯 v1.6.4 需求：2個功能按鈕，故事接龍改用指令
 * 🔘 2個按鈕：📊生成詳情、📤分享卡片
 * 🎪 故事接龍：使用指令 `！故事接龍 新內容`
 *
 * 📋 調用鏈支援：
 * 🔵 被動發圖鏈：handleAIImageGeneration → replyWithImage (imageStory有值)
 * 🟡 偽主動發圖鏈：execute_reply_cost_saver_response → replyWithImage (imageStory需傳入)
 */
function replyWithImage(replyToken, imageResult, originalPrompt, userId = null, imageStory = null) {
  // 🎲 準備顯示資訊（移到外面避免 catch 塊中 undefined）
  const seedValue = imageResult.seed || Math.floor(Math.random() * 1000000); // 備用隨機SEED
  const modelName = imageResult.modelUsed || '未知模型';

  try {
    const config = getConfig();

    if (!config.lineChannelAccessToken) {
      throw new Error('LINE Channel Access Token 未設定');
    }

    // 🔧 v1.6.5 修正：將imageStory添加到imageResult中，供分享按鈕使用
    console.log(`🔍 [調試] replyWithImage收到的imageStory參數: ${imageStory ? imageStory.substring(0, 50) + '...' : 'null'}`);
    if (imageStory) {
      imageResult.imageStory = imageStory;
      console.log(`📖 已將故事內容添加到imageResult中，長度: ${imageStory.length}`);
    } else {
      console.log(`📖 無故事內容傳入，imageResult.imageStory將為undefined`);
    }
    console.log(`🔍 [調試] 分享按鈕將使用: ${imageResult.imageStory || '生成失敗'}`);

    console.log(`🎨 使用 Flex Message v4.0 發送圖片+互動按鈕回覆`);

    // 📋 準備圖片 URL（優先使用 Cloudinary）
    let imageUrl;
    if (imageResult.cloudinaryUrl) {
      imageUrl = imageResult.cloudinaryUrl;
      console.log(`✅ 使用 Cloudinary 圖片: ${imageUrl}`);
    } else if (imageResult.driveUrl) {
      // Google Drive 需要轉換為可公開顯示的格式
      imageUrl = imageResult.driveUrl.replace('/view?usp=sharing', '').replace('/file/d/', '/uc?export=view&id=').replace('/edit?usp=sharing', '');
      console.log(`📁 使用 Google Drive 圖片: ${imageUrl}`);
    } else {
      throw new Error('無法獲取有效的圖片 URL');
    }

    console.log(`🎲 SEED: ${seedValue}, 🤖 模型: ${modelName}`);

    // 🌟 構建 v4.0 Flex Message（含4個互動按鈕）
    // 🔧 限制 altText 長度不超過 350 字符
    const maxPromptLength = 300;
    const truncatedPrompt = originalPrompt.length > maxPromptLength
      ? originalPrompt.substring(0, maxPromptLength) + '...'
      : originalPrompt;

    const flexMessage = {
      type: "flex",
      altText: `🎨 ${truncatedPrompt} (SEED: ${seedValue})`,
      contents: {
        type: "bubble",
        hero: {
          type: "image",
          url: imageUrl,
          size: "full",
          aspectRatio: "1:1",
          aspectMode: "cover",
          action: {
            type: "uri",
            uri: imageUrl // 點擊圖片查看原圖
          }
        },
        body: {
          type: "box",
          layout: "vertical",
          spacing: "md",
          paddingAll: "20px",
          contents: [
            // 🆕 v1.6.3：顯示用戶輸入的提示詞，而不是"AI圖片生成完成"
            {
              type: "text",
              text: originalPrompt,
              size: "lg",
              weight: "bold",
              color: "#333333",
              wrap: true
            }
          ]
        },
        footer: {
          type: "box",
          layout: "vertical",
          spacing: "sm",
          paddingAll: "20px",
          contents: [
            // 🔘 第二排按鈕：分享卡片（移除故事接龍按鈕，改用指令）
            {
              type: "box",
              layout: "horizontal",
              spacing: "sm",
              contents: [
                {
                  type: "button",
                  action: {
                    type: "postback",
                    label: "📤 分享故事",
                    data: createSafePostbackDataV2('share_story', seedValue, modelName, originalPrompt, {
                      imageUrl: imageUrl,
                      imageStory: imageStory,  // 🔧 修復：使用傳入的imageStory參數而不是imageResult.imageStory
                      seed: seedValue  // 🔧 添加seed以便備用查找
                    })
                  },
                  style: "primary",
                  color: "#50C878",
                  height: "sm"
                }
              ]
            },
            // 🆕 v4.1：底部文字 + SEED 顯示
            {
              type: "box",
              layout: "horizontal",
              margin: "md",
              contents: [
                {
                  type: "text",
                  text: config.footer,
                  size: "xs",
                  color: "#999999",
                  flex: 1
                },
                {
                  type: "text",
                  text: `${seedValue}`,
                  size: "xs",
                  color: "#999999",
                  align: "end"
                }
              ]
            }
          ]
        }
      }
    };

    // 📤 發送 Flex Message
    const url = 'https://api.line.me/v2/bot/message/reply';
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: [flexMessage]
    });

    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + config.lineChannelAccessToken
      },
      payload: payload
    });

    if (response.getResponseCode() !== 200) {
      throw new Error(`LINE API 回應錯誤: ${response.getContentText()}`);
    }

    console.log(`✅ Flex Message v4.0 圖片回覆成功發送，SEED: ${seedValue}`);

    // 🆕 v1.6.3：記錄圖片歷史（用於故事接龍功能）
    if (userId) {
      recordImageHistory(userId, seedValue, originalPrompt, imageUrl, modelName);
    } else {
      console.log(`⚠️ 無法記錄圖片歷史：userId 未提供`);
    }

    logActivity('Reply', 'Flex圖片回覆v4.1成功', 'Success', 'image', 'sendFlexImageReplyV4', `SEED: ${seedValue}, 模型: ${modelName}, 按鈕數: 3, Cloudinary: ${!!imageResult.cloudinaryUrl}`);

  } catch (error) {
    console.error('Flex Message v4.0 圖片回覆錯誤:', error);

    // 🆘 備用方案：使用傳統的分離訊息，並顯示錯誤原因
    console.log('🔄 改用備用方案：分離發送圖片和文字，並報告錯誤');

    try {
      // 🔧 重新獲取 config（修復 "config is not defined" 錯誤）
      const fallbackConfig = getConfig();

      // 先發送圖片
      let originalContentUrl, previewImageUrl;

      if (imageResult.cloudinaryUrl) {
        originalContentUrl = imageResult.cloudinaryUrl;
        previewImageUrl = imageResult.cloudinaryUrl.replace('/upload/', '/upload/w_240,h_240,c_fit/');
      } else if (imageResult.fileId) {
        originalContentUrl = `https://drive.google.com/uc?export=view&id=${imageResult.fileId}`;
        previewImageUrl = `https://drive.google.com/thumbnail?id=${imageResult.fileId}&sz=w240`;
      } else {
        throw new Error('備用方案也無法獲取圖片 URL');
      }

      // 🚨 錯誤報告訊息（要求用戶通知開發者）
      const errorReportText = `🎨 圖片生成失敗

🎲 SEED: ${seedValue || 'N/A'}
🤖 模型: ${imageResult.modelUsed || '未知模型'}

⚠️ 互動按鈕載入失敗
🔧 錯誤原因: ${error.message}

📞 請協助回報開發者：
- 截圖此訊息
- 註明發生時間: ${new Date().toLocaleString('zh-TW')}
- 聯繫管理員進行修復

💡 您仍可正常使用圖片，但暫時無法使用生成詳情、分享故事等互動功能。`;

      const fallbackMessages = [
        {
          type: 'image',
          originalContentUrl: originalContentUrl,
          previewImageUrl: previewImageUrl
        },
        {
          type: 'text',
          text: errorReportText
        }
      ];

      const fallbackUrl = 'https://api.line.me/v2/bot/message/reply';
      const fallbackPayload = JSON.stringify({
        replyToken: replyToken,
        messages: fallbackMessages
      });

      const fallbackResponse = UrlFetchApp.fetch(fallbackUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ' + fallbackConfig.lineChannelAccessToken
        },
        payload: fallbackPayload
      });

      if (fallbackResponse.getResponseCode() === 200) {
        console.log('✅ 備用方案發送成功，已提示用戶回報錯誤');
        logActivity('Reply', '備用圖片回覆成功', 'Success', 'image', 'replyWithImage', `SEED: ${imageResult.seed}, 錯誤原因: ${error.message}, 已要求用戶回報`);
      } else {
        throw new Error(`備用方案也失敗: ${fallbackResponse.getContentText()}`);
      }

    } catch (fallbackError) {
      console.error('備用方案也失敗:', fallbackError);
      // 最後的備用：純文字訊息，包含完整錯誤報告
      const finalFallbackText = `🎨 圖片生成失敗

🎲 SEED: ${imageResult.seed || 'N/A'}
🤖 模型: ${imageResult.modelUsed || '未知模型'}

❌ 系統錯誤 - 圖片顯示失敗
🔧 主要錯誤: ${error.message}
🔧 備用錯誤: ${fallbackError.message}

📞 請緊急聯繫開發者：
- 錯誤時間: ${new Date().toLocaleString('zh-TW')}
- 錯誤程度: 嚴重（圖片無法顯示）
- 建議立即檢查系統配置

📁 請查看 Google Drive 中的圖片檔案作為備用

🖼️ 圖片連結：
${imageResult.cloudinaryUrl || imageResult.driveUrl || '連結獲取失敗'}

💡 您可以點擊上方連結查看圖片`;

      replyMessage(replyToken, finalFallbackText);

      // 記錄嚴重錯誤
      logActivity('System', '嚴重圖片回覆錯誤', 'Failure', 'image', 'replyWithImage', `主要錯誤: ${error.message}, 備用錯誤: ${fallbackError.message}, SEED: ${imageResult.seed}`);
    }
  }
}
