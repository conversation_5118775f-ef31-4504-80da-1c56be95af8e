/*
 * 檔案: core_utils_config.gs
 * 分類: core
 * 功能開關: -
 * 描述: 配置管理和動態配置讀取
 * 依賴: [core_config.gs]
 * 最後更新: 2025-07-11
 */

// == 配置管理模組 ==
// 提供動態配置讀取和多組 API Key 管理功能

// 🆕 v1.5.1 動態配置讀取函數 - 根據標題名稱動態讀取配置值
// 🔧 v2.6.3 支援多組 Gemini API Key 輪換機制
/**
 * 根據設定工作表中的標題名稱，動態獲取對應的設定值。
 * 支援垂直結構（APIKEY工作表：A欄標題，B欄值）和水平結構。
 * @param {string} key - 要查找的設定項標題名稱 (例如 "lineChannelAccessToken")。
 * @param {string} [sheetName='APIKEY'] - 設定所在的工作表名稱，預設為 'APIKEY'。
 * @returns {string|null} 找到的設定值，如果沒找到則返回 null。
 */
function getConfigValue(key, sheetName = 'APIKEY') {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const sheet = ss.getSheetByName(sheetName);
    if (!sheet) {
      console.error(`找不到工作表: ${sheetName}`);
      return null;
    }

    // 🔧 v1.5.1 修復：支援 APIKEY 工作表的垂直結構
    if (sheetName === 'APIKEY') {
      // 建立變數名到實際標題的對應表
      const keyMappings = {
        'lineChannelAccessToken': 'LINE Channel Access Token',
        'lineChannelSecret': 'LINE Channel Secret',
        'geminiApiKey': 'Gemini API Key',
        'geminiApiKeyRotationIndex': 'Gemini API Key當前輪換索引',
        'geminiApiKeyLastUpdateTime': 'Gemini API Key最後更新時間',
        'youtubeApiKey': 'YouTube Data API Key',
        'threadsUserId': 'Threads User ID',
        'threadsAccessToken': 'Threads Access Token',
        'cloudinaryCloudName': 'Cloudinary Cloud Name',
        'cloudinaryApiKey': 'Cloudinary API Key',
        'cloudinaryApiSecret': 'Cloudinary API Secret',
        'folderId': 'Google Drive Folder ID',
        'googleSearchApiKey': 'Google Search API Key',
        'googleSearchEngineId': 'Google Search Engine ID',
        'generalModel': '一般功能模型',
        'visionModel': '圖片分析模型',
        'ttsModel': '語音合成模型',
        'audioDialogModel': '對話音頻模型',
        'embeddingModel': '嵌入向量模型',
        'fastModel': '高效率模型',
        'imageGenModel': '圖片生成模型',
        'gcpProjectId': 'GCP Project ID',
        'gcpRegion': 'GCP Region',
        'footer': '系統頁尾顯示',
        'SCRIPT_VERSION': '腳本版本',
        'fixed_seed': '角色設定',
        // 🎭 角色一致性配置映射
        'fixedSeed': '固定SEED',
        'basePrompt': '基礎圖像提示詞'
      };

      // 獲取實際要搜尋的標題
      let actualTitle = keyMappings[key];

      // 🎛️ 支援功能開關：如果是功能開關，直接使用原始鍵名
      if (!actualTitle && key.startsWith('功能開關_')) {
        actualTitle = key;
      }

      if (!actualTitle) {
        console.error(`未定義的配置項: ${key}`);
        return null;
      }

      // APIKEY 工作表使用垂直結構：A欄是標題，B欄是值
      const lastRow = sheet.getLastRow();
      if (lastRow < 2) return null;

      const data = sheet.getRange(1, 1, lastRow, 2).getValues();

      for (let i = 0; i < data.length; i++) {
        const title = data[i][0]; // A欄：標題
        const value = data[i][1]; // B欄：值

        if (title && title.toString() === actualTitle) {
          return value || '';
        }
      }

      console.error(`在工作表 "${sheetName}" 中找不到設定項: ${actualTitle}`);
      return null;

    } else {
      // 其他工作表使用水平結構：第1行是標題，第2行是值
      const data = sheet.getRange(1, 1, 2, sheet.getLastColumn()).getValues();
      const headers = data[0]; // 第一行是標題
      const values = data[1];  // 第二行是值

      const columnIndex = headers.indexOf(key);

      if (columnIndex !== -1) {
        return values[columnIndex] || '';
      } else {
        console.error(`在工作表 "${sheetName}" 中找不到設定項: ${key}`);
        return null;
      }
    }
  } catch (e) {
    console.error(`讀取設定值 "${key}" 時發生錯誤: ${e}`);
    return null;
  }
}

// 🔧 v2.6.3 多組 Gemini API Key 管理系統
/**
 * 解析多組 API Key（支援逗號分隔或換行分隔）
 * @param {string} keyString - API Key 字串
 * @returns {Array<string>} API Key 陣列
 */
function parseMultipleApiKeys(keyString) {
  if (!keyString) return [];

  // 支援 Ctrl+Enter 換行分隔（\r\n）或逗號分隔
  const keys = keyString.split(/[\r\n,]+/)
    .map(key => key.trim())
    .filter(key => key.length > 0);

  console.log(`🔑 解析到 ${keys.length} 組 API Key`);
  return keys;
}

/**
 * 獲取當前可用的 Gemini API Key
 * @returns {string} 當前可用的 API Key
 */
function getCurrentGeminiApiKey() {
  try {
    const keyString = getConfigValue('geminiApiKey');
    const keys = parseMultipleApiKeys(keyString);

    if (keys.length === 0) {
      throw new Error('未設定 Gemini API Key');
    }

    if (keys.length === 1) {
      return keys[0]; // 單組 Key 直接返回
    }

    // 多組 Key 使用輪換機制
    const currentIndex = getApiKeyRotationIndex();
    const selectedKey = keys[currentIndex % keys.length];

    console.log(`🔄 使用第 ${(currentIndex % keys.length) + 1} 組 API Key (共 ${keys.length} 組)`);
    return selectedKey;

  } catch (error) {
    console.error('❌ 獲取 Gemini API Key 失敗:', error);
    throw error;
  }
}

/**
 * 獲取 API Key 輪換索引（從APIKEY工作表讀取）
 * @returns {number} 當前輪換索引
 */
function getApiKeyRotationIndex() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');

    if (!apikeySheet) {
      console.error('❌ APIKEY 工作表不存在');
      return 0;
    }

    // 檢查是否已有輪換索引行
    const indexTitle = apikeySheet.getRange('A5').getValue();
    if (indexTitle !== 'Gemini API Key當前輪換索引') {
      // 初始化輪換索引行
      apikeySheet.getRange('A5').setValue('Gemini API Key當前輪換索引');
      apikeySheet.getRange('B5').setValue(0);
      apikeySheet.getRange('A6').setValue('Gemini API Key最後更新時間');
      apikeySheet.getRange('B6').setValue(new Date());
      console.log('✅ 初始化 APIKEY 工作表輪換狀態');
    }

    const currentIndex = apikeySheet.getRange('B5').getValue() || 0;
    return parseInt(currentIndex);

  } catch (error) {
    console.error('❌ 獲取輪換索引失敗:', error);
    return 0; // 默認返回 0
  }
}

/**
 * 更新 API Key 輪換索引（429 錯誤時調用）
 */
function rotateToNextApiKey() {
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');

    if (!apikeySheet) {
      console.error('❌ APIKEY 工作表不存在');
      return;
    }

    const currentIndex = getApiKeyRotationIndex();
    const nextIndex = currentIndex + 1;

    // 更新APIKEY工作表中的輪換狀態
    apikeySheet.getRange('B5').setValue(nextIndex);
    apikeySheet.getRange('B6').setValue(new Date());

    console.log(`🔄 API Key 輪換：${currentIndex} → ${nextIndex}`);

    // 記錄輪換日誌
    logActivity('System', 'API Key 輪換', 'Success', 'api', 'rotateToNextApiKey',
                `從索引 ${currentIndex} 輪換到 ${nextIndex}`);

  } catch (error) {
    console.error('❌ API Key 輪換失敗:', error);
  }
}

// 1. 🚀 Configuration - 從 APIKEY 工作表讀取設定（支援所有功能模型）v1.5.1 動態版本
function getConfig() {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('APIKEY');
  if (!sheet) {
    throw new Error('找不到 APIKEY 工作表，請先執行 initializeSheets()');
  }

  // 🆕 v1.5.1：使用動態配置讀取，向後兼容硬編碼方式
  // 🔧 v2.6.3：支援多組 Gemini API Key 輪換，欄位位置已調整
  return {
    lineChannelAccessToken: getConfigValue('lineChannelAccessToken') || sheet.getRange('B1').getValue() || '',
    lineChannelSecret: getConfigValue('lineChannelSecret') || sheet.getRange('B2').getValue() || '',
    geminiApiKey: getCurrentGeminiApiKey(), // 🔧 使用輪換機制
    geminiApiKeyRotationIndex: getConfigValue('geminiApiKeyRotationIndex') || sheet.getRange('B5').getValue() || 0,
    geminiApiKeyLastUpdateTime: getConfigValue('geminiApiKeyLastUpdateTime') || sheet.getRange('B6').getValue() || '',
    youtubeApiKey: getConfigValue('youtubeApiKey') || sheet.getRange('B7').getValue() || '', // 從B5移至B7
    threadsUserId: getConfigValue('threadsUserId') || sheet.getRange('B8').getValue() || '', // 從B6移至B8
    threadsAccessToken: getConfigValue('threadsAccessToken') || sheet.getRange('B9').getValue() || '',
    cloudinaryCloudName: getConfigValue('cloudinaryCloudName') || sheet.getRange('B10').getValue() || '',
    cloudinaryApiKey: getConfigValue('cloudinaryApiKey') || sheet.getRange('B11').getValue() || '',
    cloudinaryApiSecret: getConfigValue('cloudinaryApiSecret') || sheet.getRange('B12').getValue() || '',
    folderId: getConfigValue('folderId') || sheet.getRange('B13').getValue() || '',
    googleSearchApiKey: getConfigValue('googleSearchApiKey') || sheet.getRange('B14').getValue() || '',
    googleSearchEngineId: getConfigValue('googleSearchEngineId') || sheet.getRange('B15').getValue() || '',
    generalModel: getConfigValue('generalModel') || sheet.getRange('B16').getValue() || 'gemini-2.5-flash',
    visionModel: getConfigValue('visionModel') || sheet.getRange('B17').getValue() || 'gemini-2.5-pro',
    ttsModel: getConfigValue('ttsModel') || sheet.getRange('B18').getValue() || 'gemini-2.5-flash-preview-tts',
    audioDialogModel: getConfigValue('audioDialogModel') || sheet.getRange('B19').getValue() || 'gemini-2.5-flash-preview-native-audio-dialog',
    embeddingModel: getConfigValue('embeddingModel') || sheet.getRange('B20').getValue() || 'gemini-embedding-exp',
    fastModel: getConfigValue('fastModel') || sheet.getRange('B21').getValue() || 'gemini-2.5-flash-lite-preview-06-17',
    imageGenModel: getConfigValue('imageGenModel') || sheet.getRange('B22').getValue() || 'gemini-2.0-flash-preview-image-generation',
    gcpProjectId: getConfigValue('gcpProjectId') || sheet.getRange('B23').getValue() || '',
    gcpRegion: getConfigValue('gcpRegion') || sheet.getRange('B24').getValue() || 'us-central1',
    footer: getConfigValue('footer') || sheet.getRange('B25').getValue() || '❤️ 台灣智能經貿協會'
  };
}

/**
 * 🎛️ 強健的功能開關檢查函數
 * 🔧 修復：處理可能的空白字符和類型問題
 * @param {string} featureName - 功能名稱（不含 '功能開關_' 前綴）
 * @param {string} sheetName - 工作表名稱，預設為 'APIKEY'
 * @returns {boolean} 功能是否啟用
 */
function isFeatureToggleEnabled(featureName, sheetName = 'APIKEY') {
  try {
    const key = `功能開關_${featureName}`;
    const rawValue = getConfigValue(key, sheetName);

    if (!rawValue) {
      console.log(`⚠️ 功能開關 ${key} 未找到或為空，預設為關閉`);
      return false;
    }

    // 多重檢查邏輯，處理各種可能的值格式
    const isEnabled = rawValue === 'TRUE' ||
                     rawValue === true ||
                     rawValue === 'true' ||
                     rawValue.toString().trim().toUpperCase() === 'TRUE';

    console.log(`🎛️ 功能開關檢查: ${key} = "${rawValue}" → ${isEnabled ? '✅ 啟用' : '❌ 關閉'}`);

    return isEnabled;

  } catch (error) {
    console.error(`❌ 檢查功能開關 ${featureName} 失敗:`, error);
    return false;
  }
}
