/*
 * 檔案: 重構驗證_core_module_manager_拆分檢查_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 驗證 core_module_manager.gs 重構拆分後的函數完整性
 * 依賴: [core_module_*.gs]
 * 最後更新: 2025-07-11
 */

/**
 * 🧪 core_module_manager 重構驗證測試
 * 按照 SOP 指南進行重構後的函數完整性檢查
 */
function 重構驗證_core_module_manager_拆分檢查_debug() {
  console.log('🧪 ===== core_module_manager 重構驗證測試 =====');
  
  const results = {
    timestamp: new Date(),
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    },
    tests: {
      functionExistence: [],
      functionCalls: [],
      moduleIntegrity: [],
      safetyNet: []
    },
    issues: [],
    recommendations: []
  };

  try {
    // ===== 🔍 測試1：檢查關鍵函數是否存在 =====
    console.log('\n🔍 測試1：檢查關鍵函數是否存在...');
    
    const keyFunctions = [
      // 模組管理核心函數
      { name: 'autoManageModules', module: 'core_module_manager_core.gs', critical: true },
      { name: 'activateModule', module: 'core_module_manager_core.gs', critical: true },
      { name: 'deactivateModule', module: 'core_module_manager_core.gs', critical: true },
      { name: 'isFeatureEnabled', module: 'core_feature_toggle.gs', critical: true },
      
      // 檔案操作函數
      { name: 'moveFileToRoot', module: 'core_module_file_ops.gs', critical: true },
      { name: 'moveFileToSleeping', module: 'core_module_file_ops.gs', critical: true },
      { name: 'checkFileExists', module: 'core_module_file_ops.gs', critical: true },
      { name: 'getFileLocation', module: 'core_module_file_ops.gs', critical: false },
      
      // 映射表函數
      { name: 'getModuleFileMapping', module: 'core_module_mapping.gs', critical: true },
      { name: 'getModuleInfo', module: 'core_module_mapping.gs', critical: true },
      { name: 'getAllFeatureNames', module: 'core_module_mapping.gs', critical: false },
      
      // 安全網函數
      { name: 'runSafetyNetCheck', module: 'core_module_safety.gs', critical: true },
      { name: 'checkFileConsistency', module: 'core_module_safety.gs', critical: false },
      { name: 'findMissingFiles', module: 'core_module_safety.gs', critical: false }
    ];

    keyFunctions.forEach(funcInfo => {
      results.summary.totalTests++;
      try {
        const func = eval(funcInfo.name);
        if (typeof func === 'function') {
          results.tests.functionExistence.push({
            function: funcInfo.name,
            module: funcInfo.module,
            status: 'PASS',
            critical: funcInfo.critical
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${funcInfo.name}: 存在且可調用`);
        } else {
          results.tests.functionExistence.push({
            function: funcInfo.name,
            module: funcInfo.module,
            status: 'FAIL',
            error: '不是函數',
            critical: funcInfo.critical
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${funcInfo.name}: 不是函數`);
          
          if (funcInfo.critical) {
            results.issues.push(`關鍵函數 ${funcInfo.name} 不存在或不可調用`);
          }
        }
      } catch (error) {
        results.tests.functionExistence.push({
          function: funcInfo.name,
          module: funcInfo.module,
          status: 'FAIL',
          error: error.message,
          critical: funcInfo.critical
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${funcInfo.name}: ${error.message}`);
        
        if (funcInfo.critical) {
          results.issues.push(`關鍵函數 ${funcInfo.name} 缺失: ${error.message}`);
        }
      }
    });

    // ===== 🔍 測試2：檢查函數調用是否正常 =====
    console.log('\n🔍 測試2：檢查函數調用是否正常...');
    
    const functionCallTests = [
      {
        name: 'getModuleFileMapping 調用測試',
        test: () => {
          const mapping = getModuleFileMapping();
          return mapping && typeof mapping === 'object' && mapping.IMAGE_GENERATION !== undefined;
        }
      },
      {
        name: 'getModuleInfo 調用測試',
        test: () => {
          const info = getModuleInfo('IMAGE_GENERATION');
          return info && info.prefix && info.files;
        }
      },
      {
        name: 'isFeatureEnabled 調用測試',
        test: () => {
          const enabled = isFeatureEnabled('IMAGE_GENERATION');
          return typeof enabled === 'boolean';
        }
      },
      {
        name: 'checkFileExists 調用測試',
        test: () => {
          const exists = checkFileExists('test_file.gs', 'root');
          return typeof exists === 'boolean';
        }
      }
    ];

    functionCallTests.forEach(testInfo => {
      results.summary.totalTests++;
      try {
        const result = testInfo.test();
        if (result) {
          results.tests.functionCalls.push({
            test: testInfo.name,
            status: 'PASS'
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${testInfo.name}: 通過`);
        } else {
          results.tests.functionCalls.push({
            test: testInfo.name,
            status: 'FAIL',
            error: '測試返回 false'
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${testInfo.name}: 測試返回 false`);
          results.issues.push(`函數調用測試失敗: ${testInfo.name}`);
        }
      } catch (error) {
        results.tests.functionCalls.push({
          test: testInfo.name,
          status: 'FAIL',
          error: error.message
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${testInfo.name}: ${error.message}`);
        results.issues.push(`函數調用測試錯誤: ${testInfo.name} - ${error.message}`);
      }
    });

    // ===== 🔍 測試3：檢查模組完整性 =====
    console.log('\n🔍 測試3：檢查模組完整性...');
    
    const moduleFiles = [
      'core_module_mapping.gs',
      'core_module_manager_core.gs',
      'core_module_file_ops.gs',
      'core_module_safety.gs'
    ];

    moduleFiles.forEach(moduleName => {
      results.summary.totalTests++;
      try {
        let moduleExists = false;
        let keyFunction = '';
        
        switch (moduleName) {
          case 'core_module_mapping.gs':
            keyFunction = 'getModuleFileMapping';
            break;
          case 'core_module_manager_core.gs':
            keyFunction = 'autoManageModules';
            break;
          case 'core_module_file_ops.gs':
            keyFunction = 'moveFileToRoot';
            break;
          case 'core_module_safety.gs':
            keyFunction = 'runSafetyNetCheck';
            break;
        }
        
        try {
          const func = eval(keyFunction);
          moduleExists = typeof func === 'function';
        } catch (e) {
          moduleExists = false;
        }
        
        if (moduleExists) {
          results.tests.moduleIntegrity.push({
            module: moduleName,
            status: 'PASS',
            keyFunction: keyFunction
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${moduleName}: 模組完整`);
        } else {
          results.tests.moduleIntegrity.push({
            module: moduleName,
            status: 'FAIL',
            error: `關鍵函數 ${keyFunction} 不存在`,
            keyFunction: keyFunction
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${moduleName}: 模組缺失或不完整`);
          results.issues.push(`模組 ${moduleName} 缺失或不完整`);
        }
        
      } catch (error) {
        results.tests.moduleIntegrity.push({
          module: moduleName,
          status: 'FAIL',
          error: error.message
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${moduleName}: ${error.message}`);
        results.issues.push(`模組檢查錯誤: ${moduleName} - ${error.message}`);
      }
    });

    // ===== 🔍 測試4：安全網檢查 =====
    console.log('\n🔍 測試4：安全網檢查...');
    
    results.summary.totalTests++;
    try {
      const safetyResults = runSafetyNetCheck();
      if (safetyResults && !safetyResults.error) {
        results.tests.safetyNet.push({
          test: '安全網檢查',
          status: 'PASS',
          details: safetyResults
        });
        results.summary.passedTests++;
        console.log(`  ✅ 安全網檢查: 通過`);
        
        // 檢查是否有建議
        if (safetyResults.recommendations && safetyResults.recommendations.length > 0) {
          console.log(`  💡 安全網建議: ${safetyResults.recommendations.length} 項`);
          safetyResults.recommendations.forEach(rec => {
            if (rec.type === 'error' || rec.type === 'warning') {
              results.issues.push(`安全網建議: ${rec.message}`);
            }
          });
        }
      } else {
        results.tests.safetyNet.push({
          test: '安全網檢查',
          status: 'FAIL',
          error: safetyResults ? safetyResults.error : '未知錯誤'
        });
        results.summary.failedTests++;
        console.log(`  ❌ 安全網檢查: 失敗`);
        results.issues.push(`安全網檢查失敗: ${safetyResults ? safetyResults.error : '未知錯誤'}`);
      }
    } catch (error) {
      results.tests.safetyNet.push({
        test: '安全網檢查',
        status: 'FAIL',
        error: error.message
      });
      results.summary.failedTests++;
      console.log(`  ❌ 安全網檢查: ${error.message}`);
      results.issues.push(`安全網檢查錯誤: ${error.message}`);
    }

    // ===== 📊 生成測試報告 =====
    console.log('\n📊 ===== 測試報告 =====');
    console.log(`總測試數: ${results.summary.totalTests}`);
    console.log(`通過: ${results.summary.passedTests}`);
    console.log(`失敗: ${results.summary.failedTests}`);
    console.log(`成功率: ${((results.summary.passedTests / results.summary.totalTests) * 100).toFixed(1)}%`);

    if (results.issues.length > 0) {
      console.log('\n⚠️ 發現問題:');
      results.issues.forEach(issue => console.log(`  - ${issue}`));
      
      // 生成建議
      results.recommendations.push('檢查缺失的函數是否已正確遷移到對應模組');
      results.recommendations.push('確認所有模組檔案都已正確創建');
      results.recommendations.push('執行安全網檢查以發現潛在問題');
    } else {
      console.log('\n✅ 所有測試通過，重構成功！');
      results.recommendations.push('重構驗證通過，可以進行下一步測試');
    }

    return results;

  } catch (error) {
    console.error('❌ 重構驗證測試失敗:', error);
    results.issues.push(`測試執行失敗: ${error.message}`);
    return results;
  }
}
