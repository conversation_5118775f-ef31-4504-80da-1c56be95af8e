/*
 * 檔案: 清理不必要功能開關_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 清理 APIKEY 工作表中不必要的功能開關
 * 依賴: [core_utils_config.gs]
 * 最後更新: 2025-07-11
 */

/**
 * 🧹 清理不必要的功能開關
 * 移除系統核心功能的開關，這些功能應該永遠啟用
 */
function 清理不必要功能開關_debug() {
  console.log('🧹 ===== 清理不必要功能開關 =====');
  
  const results = {
    timestamp: new Date(),
    removedToggles: [],
    keptToggles: [],
    errors: [],
    summary: ''
  };

  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      throw new Error('找不到 APIKEY 工作表');
    }

    // 🚫 需要移除的功能開關（系統核心功能）
    const unnecessaryToggles = [
      'LINE_WEBHOOK',        // LINE Bot 核心功能
      'AI_PROMPTS',          // AI 提示詞核心功能
      'GROUP_CHAT_TRACKING'  // 與 GROUP_MEMBER_QUERY 重複
    ];

    // ✅ 應該保留的功能開關（可選功能）
    const necessaryToggles = [
      'IMAGE_GENERATION',
      'TEXT_TO_SPEECH', 
      'CONVERSATIONAL_AUDIO',
      'NOTE_TAKING',
      'FILE_QUERY',
      'CONVERSATION_REVIEW',
      'GROUP_MEMBER_QUERY',
      'SOCIAL_MEDIA_POST',
      'KNOWLEDGE_BASE',
      'SMART_RESPONDER',
      'PIGGYBACK_SYSTEM'
    ];

    console.log('\n🔍 掃描 APIKEY 工作表中的功能開關...');
    
    const data = apikeySheet.getDataRange().getValues();
    const rowsToDelete = [];

    for (let i = 0; i < data.length; i++) {
      const cellA = data[i][0];
      
      if (typeof cellA === 'string' && cellA.startsWith('功能開關_')) {
        const featureName = cellA.replace('功能開關_', '');
        const rowNumber = i + 1;
        
        if (unnecessaryToggles.includes(featureName)) {
          // 標記要刪除的行
          rowsToDelete.push({
            row: rowNumber,
            featureName: featureName,
            currentValue: data[i][1],
            reason: '系統核心功能，不需要開關'
          });
          
          results.removedToggles.push({
            name: featureName,
            row: rowNumber,
            value: data[i][1],
            reason: '系統核心功能'
          });
          
          console.log(`  🚫 將移除: 功能開關_${featureName} (第${rowNumber}行) - 系統核心功能`);
          
        } else if (necessaryToggles.includes(featureName)) {
          results.keptToggles.push({
            name: featureName,
            row: rowNumber,
            value: data[i][1],
            reason: '可選功能，保留開關'
          });
          
          console.log(`  ✅ 保留: 功能開關_${featureName} (第${rowNumber}行) - 可選功能`);
          
        } else {
          results.keptToggles.push({
            name: featureName,
            row: rowNumber,
            value: data[i][1],
            reason: '未知功能，保留以防萬一'
          });
          
          console.log(`  ❓ 保留: 功能開關_${featureName} (第${rowNumber}行) - 未知功能`);
        }
      }
    }

    // 🗑️ 執行刪除（從後往前刪除，避免行號變化）
    if (rowsToDelete.length > 0) {
      console.log(`\n🗑️ 準備刪除 ${rowsToDelete.length} 個不必要的功能開關...`);
      
      // 排序：從大到小的行號
      rowsToDelete.sort((a, b) => b.row - a.row);
      
      for (const item of rowsToDelete) {
        try {
          apikeySheet.deleteRow(item.row);
          console.log(`  ✅ 已刪除第 ${item.row} 行: 功能開關_${item.featureName}`);
        } catch (error) {
          console.error(`  ❌ 刪除第 ${item.row} 行失敗: ${error.message}`);
          results.errors.push(`刪除第 ${item.row} 行失敗: ${error.message}`);
        }
      }
    } else {
      console.log('\n✅ 沒有發現需要移除的功能開關');
    }

    // 📊 生成摘要
    results.summary = `清理完成：移除 ${results.removedToggles.length} 個不必要開關，保留 ${results.keptToggles.length} 個必要開關`;
    
    console.log('\n📊 ===== 清理摘要 =====');
    console.log(results.summary);
    
    if (results.removedToggles.length > 0) {
      console.log('\n🚫 已移除的功能開關:');
      results.removedToggles.forEach(item => {
        console.log(`  - ${item.name}: ${item.reason}`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ 錯誤:');
      results.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('\n💡 說明:');
    console.log('  - LINE_WEBHOOK: LINE Bot 核心功能，永遠啟用');
    console.log('  - AI_PROMPTS: AI 回應核心功能，永遠啟用');
    console.log('  - GROUP_CHAT_TRACKING: 功能已合併到 GROUP_MEMBER_QUERY');

    return results;

  } catch (error) {
    console.error('❌ 清理功能開關失敗:', error);
    results.errors.push(`清理失敗: ${error.message}`);
    return results;
  }
}

/**
 * 🔍 檢查當前功能開關狀態
 * 不執行清理，只檢查和報告
 */
function 檢查功能開關狀態_debug() {
  console.log('🔍 ===== 檢查功能開關狀態 =====');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      throw new Error('找不到 APIKEY 工作表');
    }

    const data = apikeySheet.getDataRange().getValues();
    const foundToggles = [];

    for (let i = 0; i < data.length; i++) {
      const cellA = data[i][0];
      
      if (typeof cellA === 'string' && cellA.startsWith('功能開關_')) {
        const featureName = cellA.replace('功能開關_', '');
        foundToggles.push({
          name: featureName,
          row: i + 1,
          value: data[i][1]
        });
      }
    }

    console.log(`\n📋 發現 ${foundToggles.length} 個功能開關:`);
    foundToggles.forEach(toggle => {
      console.log(`  - 功能開關_${toggle.name}: "${toggle.value}" (第${toggle.row}行)`);
    });

    // 檢查不必要的開關
    const unnecessaryToggles = ['LINE_WEBHOOK', 'AI_PROMPTS', 'GROUP_CHAT_TRACKING'];
    const foundUnnecessary = foundToggles.filter(t => unnecessaryToggles.includes(t.name));
    
    if (foundUnnecessary.length > 0) {
      console.log(`\n⚠️ 發現 ${foundUnnecessary.length} 個不必要的功能開關:`);
      foundUnnecessary.forEach(toggle => {
        console.log(`  - 功能開關_${toggle.name}: 系統核心功能，建議移除`);
      });
      console.log('\n💡 執行 清理不必要功能開關_debug() 來自動清理');
    } else {
      console.log('\n✅ 沒有發現不必要的功能開關');
    }

    return foundToggles;

  } catch (error) {
    console.error('❌ 檢查功能開關狀態失敗:', error);
    return [];
  }
}
