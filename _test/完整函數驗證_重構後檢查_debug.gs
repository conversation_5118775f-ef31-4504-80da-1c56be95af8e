/*
 * 檔案: 完整函數驗證_重構後檢查_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 驗證重構後所有函數的存在性和正確性
 * 依賴: [所有重構後的模組]
 * 最後更新: 2025-07-11
 */

/**
 * 🔍 完整函數驗證 - 重構後檢查
 * 驗證所有重構後的函數是否正確存在並可調用
 */
function 完整函數驗證_重構後檢查_debug() {
  console.log('🔍 ===== 完整函數驗證 - 重構後檢查 =====');
  
  const results = {
    timestamp: new Date(),
    summary: {
      totalFunctions: 0,
      existingFunctions: 0,
      missingFunctions: 0,
      successRate: 0
    },
    categories: {
      coreUtilsConfig: { tested: 0, passed: 0, functions: [] },
      coreUtilsApi: { tested: 0, passed: 0, functions: [] },
      coreUtilsHelpers: { tested: 0, passed: 0, functions: [] },
      coreUtilsReply: { tested: 0, passed: 0, functions: [] },
      coreUtilsLogging: { tested: 0, passed: 0, functions: [] },
      coreModuleMapping: { tested: 0, passed: 0, functions: [] },
      coreModuleManagerCore: { tested: 0, passed: 0, functions: [] },
      coreModuleFileOps: { tested: 0, passed: 0, functions: [] },
      coreModuleSafety: { tested: 0, passed: 0, functions: [] }
    },
    missingFunctions: [],
    issues: []
  };

  try {
    console.log('\n📋 按模組檢查函數存在性...');

    // ===== core_utils_config.gs 函數 =====
    console.log('\n🔧 檢查 core_utils_config.gs 函數...');
    const configFunctions = [
      'getConfig',
      'getConfigValue', 
      'getCurrentGeminiApiKey',
      'rotateGeminiApiKey',
      'getGeminiApiKeyStatus'
    ];
    
    results.categories.coreUtilsConfig = checkFunctionCategory(
      'core_utils_config.gs', 
      configFunctions, 
      results.missingFunctions
    );

    // ===== core_utils_api.gs 函數 =====
    console.log('\n🚀 檢查 core_utils_api.gs 函數...');
    const apiFunctions = [
      'callGemini',
      'getModelForTask',
      'callGeminiWithIntent',
      'validateModelForFunction',
      'modelSupports'
    ];
    
    results.categories.coreUtilsApi = checkFunctionCategory(
      'core_utils_api.gs', 
      apiFunctions, 
      results.missingFunctions
    );

    // ===== core_utils_helpers.gs 函數 =====
    console.log('\n🛠️ 檢查 core_utils_helpers.gs 函數...');
    const helperFunctions = [
      'normalizeText',
      'normalizeCommand',
      'isFeatureToggleEnabled',
      'extractCommand',
      'parseUserInput'
    ];
    
    results.categories.coreUtilsHelpers = checkFunctionCategory(
      'core_utils_helpers.gs', 
      helperFunctions, 
      results.missingFunctions
    );

    // ===== core_utils_reply.gs 函數 =====
    console.log('\n💬 檢查 core_utils_reply.gs 函數...');
    const replyFunctions = [
      'replyMessage',
      'replyWithAudio',
      'replyWithImage',
      'generateAudioFallbackText',
      'generateImageFallbackText'
    ];
    
    results.categories.coreUtilsReply = checkFunctionCategory(
      'core_utils_reply.gs', 
      replyFunctions, 
      results.missingFunctions
    );

    // ===== core_utils_logging.gs 函數 =====
    console.log('\n📝 檢查 core_utils_logging.gs 函數...');
    const loggingFunctions = [
      'logActivity',
      'getFileTypeInfo',
      'getVersionInfo',
      'recordImageHistory',
      'findRecentImageHistory'
    ];
    
    results.categories.coreUtilsLogging = checkFunctionCategory(
      'core_utils_logging.gs', 
      loggingFunctions, 
      results.missingFunctions
    );

    // ===== core_module_mapping.gs 函數 =====
    console.log('\n🗺️ 檢查 core_module_mapping.gs 函數...');
    const mappingFunctions = [
      'getModuleFileMapping',
      'getModuleInfo',
      'getAllFeatureNames',
      'findFeaturesByFileName',
      'validateModuleMapping'
    ];
    
    results.categories.coreModuleMapping = checkFunctionCategory(
      'core_module_mapping.gs', 
      mappingFunctions, 
      results.missingFunctions
    );

    // ===== core_module_manager_core.gs 函數 =====
    console.log('\n⚙️ 檢查 core_module_manager_core.gs 函數...');
    const managerCoreFunctions = [
      'autoManageModules',
      'activateModule',
      'deactivateModule',
      'isFeatureEnabled',
      'getAllModuleStatus'
    ];
    
    results.categories.coreModuleManagerCore = checkFunctionCategory(
      'core_module_manager_core.gs', 
      managerCoreFunctions, 
      results.missingFunctions
    );

    // ===== core_module_file_ops.gs 函數 =====
    console.log('\n📁 檢查 core_module_file_ops.gs 函數...');
    const fileOpsFunctions = [
      'moveFileToRoot',
      'moveFileToSleeping',
      'checkFileExists',
      'getFileLocation',
      'getAllFileLocations'
    ];
    
    results.categories.coreModuleFileOps = checkFunctionCategory(
      'core_module_file_ops.gs', 
      fileOpsFunctions, 
      results.missingFunctions
    );

    // ===== core_module_safety.gs 函數 =====
    console.log('\n🛡️ 檢查 core_module_safety.gs 函數...');
    const safetyFunctions = [
      'runSafetyNetCheck',
      'checkFileConsistency',
      'findMissingFiles',
      'findOrphanedFiles',
      'autoFixInconsistentLocations'
    ];
    
    results.categories.coreModuleSafety = checkFunctionCategory(
      'core_module_safety.gs', 
      safetyFunctions, 
      results.missingFunctions
    );

    // ===== 📊 計算總體統計 =====
    console.log('\n📊 ===== 總體統計 =====');
    
    Object.values(results.categories).forEach(category => {
      results.summary.totalFunctions += category.tested;
      results.summary.existingFunctions += category.passed;
    });
    
    results.summary.missingFunctions = results.summary.totalFunctions - results.summary.existingFunctions;
    results.summary.successRate = ((results.summary.existingFunctions / results.summary.totalFunctions) * 100).toFixed(1);

    console.log(`總函數數: ${results.summary.totalFunctions}`);
    console.log(`存在函數: ${results.summary.existingFunctions}`);
    console.log(`缺失函數: ${results.summary.missingFunctions}`);
    console.log(`成功率: ${results.summary.successRate}%`);

    // ===== 📋 詳細報告 =====
    if (results.missingFunctions.length > 0) {
      console.log('\n❌ 缺失的函數:');
      results.missingFunctions.forEach(func => {
        console.log(`  - ${func.name} (${func.module})`);
        results.issues.push(`函數 ${func.name} 在模組 ${func.module} 中缺失`);
      });
    } else {
      console.log('\n✅ 所有函數都存在且可調用！');
    }

    // ===== 📊 按模組統計 =====
    console.log('\n📊 按模組統計:');
    Object.entries(results.categories).forEach(([categoryName, category]) => {
      const rate = category.tested > 0 ? ((category.passed / category.tested) * 100).toFixed(1) : '0.0';
      console.log(`  ${categoryName}: ${category.passed}/${category.tested} (${rate}%)`);
    });

    return results;

  } catch (error) {
    console.error('❌ 完整函數驗證失敗:', error);
    results.issues.push(`驗證執行失敗: ${error.message}`);
    return results;
  }
}

/**
 * 🔍 檢查特定模組的函數類別
 */
function checkFunctionCategory(moduleName, functionList, missingFunctions) {
  const result = {
    tested: functionList.length,
    passed: 0,
    functions: []
  };

  functionList.forEach(funcName => {
    try {
      const func = eval(funcName);
      if (typeof func === 'function') {
        result.passed++;
        result.functions.push({
          name: funcName,
          status: 'EXISTS',
          module: moduleName
        });
        console.log(`    ✅ ${funcName}: 存在`);
      } else {
        result.functions.push({
          name: funcName,
          status: 'NOT_FUNCTION',
          module: moduleName
        });
        missingFunctions.push({
          name: funcName,
          module: moduleName,
          reason: '不是函數'
        });
        console.log(`    ❌ ${funcName}: 不是函數`);
      }
    } catch (error) {
      result.functions.push({
        name: funcName,
        status: 'MISSING',
        module: moduleName,
        error: error.message
      });
      missingFunctions.push({
        name: funcName,
        module: moduleName,
        reason: error.message
      });
      console.log(`    ❌ ${funcName}: 缺失 (${error.message})`);
    }
  });

  return result;
}
