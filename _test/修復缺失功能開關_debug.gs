/*
 * 檔案: 修復缺失功能開關_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 修復 APIKEY 工作表中缺失的功能開關
 * 依賴: [core_utils_config.gs]
 * 最後更新: 2025-07-11
 */

/**
 * 🔧 修復缺失的功能開關
 * 自動在 APIKEY 工作表中添加缺失的功能開關
 */
function 修復缺失功能開關_debug() {
  console.log('🔧 ===== 修復缺失功能開關 =====');
  
  const results = {
    timestamp: new Date(),
    addedToggles: [],
    existingToggles: [],
    errors: [],
    summary: ''
  };

  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      throw new Error('找不到 APIKEY 工作表');
    }

    // ✅ 應該存在的功能開關（基於 core_feature_toggle.gs）
    const requiredToggles = [
      'IMAGE_GENERATION',
      'IMAGE_BUTTONS',
      'IMAGE_STORY',
      'IMAGE_STORY_CONTINUATION',
      'TEXT_TO_SPEECH',
      'CONVERSATIONAL_AUDIO',
      'NOTE_TAKING',
      'FILE_QUERY',
      'CONVERSATION_REVIEW',
      'GROUP_MEMBER_QUERY',
      'SMART_RESPONDER',
      'PIGGYBACK_SYSTEM',
      'SOCIAL_MEDIA_POST',
      'DRIVE_LINK_SHARING',
      'KNOWLEDGE_BASE',        // 🔧 這個缺失了！
      'SYSTEM_HELP',
      'MODEL_EXAMPLES',
      'SMART_SEARCH',
      'EXPERIMENTAL_FEATURES'
    ];

    console.log('\n🔍 檢查現有功能開關...');
    
    const data = apikeySheet.getDataRange().getValues();
    const existingToggles = new Set();

    // 收集現有的功能開關
    for (let i = 0; i < data.length; i++) {
      const cellA = data[i][0];
      
      if (typeof cellA === 'string' && cellA.startsWith('功能開關_')) {
        const featureName = cellA.replace('功能開關_', '');
        existingToggles.add(featureName);
        results.existingToggles.push({
          name: featureName,
          row: i + 1,
          value: data[i][1]
        });
        console.log(`  ✅ 已存在: 功能開關_${featureName} = "${data[i][1]}"`);
      }
    }

    console.log('\n🔧 檢查缺失的功能開關...');
    
    // 找出缺失的功能開關
    const missingToggles = requiredToggles.filter(toggle => !existingToggles.has(toggle));
    
    if (missingToggles.length === 0) {
      console.log('✅ 所有功能開關都已存在');
      results.summary = '所有功能開關都已存在，無需修復';
      return results;
    }

    console.log(`\n🚨 發現 ${missingToggles.length} 個缺失的功能開關:`);
    missingToggles.forEach(toggle => {
      console.log(`  ❌ 缺失: 功能開關_${toggle}`);
    });

    // 🔧 添加缺失的功能開關
    console.log('\n🔧 開始添加缺失的功能開關...');
    
    const lastRow = apikeySheet.getLastRow();
    let currentRow = lastRow + 1;

    for (const toggle of missingToggles) {
      try {
        const toggleKey = `功能開關_${toggle}`;
        const defaultValue = 'true'; // 預設啟用
        
        apikeySheet.getRange(currentRow, 1).setValue(toggleKey);
        apikeySheet.getRange(currentRow, 2).setValue(defaultValue);
        
        results.addedToggles.push({
          name: toggle,
          key: toggleKey,
          value: defaultValue,
          row: currentRow
        });
        
        console.log(`  ✅ 已添加: ${toggleKey} = "${defaultValue}" (第${currentRow}行)`);
        currentRow++;
        
      } catch (error) {
        console.error(`  ❌ 添加 ${toggle} 失敗: ${error.message}`);
        results.errors.push(`添加 ${toggle} 失敗: ${error.message}`);
      }
    }

    // 📊 生成摘要
    results.summary = `修復完成：添加 ${results.addedToggles.length} 個缺失開關，保留 ${results.existingToggles.length} 個現有開關`;
    
    console.log('\n📊 ===== 修復摘要 =====');
    console.log(results.summary);
    
    if (results.addedToggles.length > 0) {
      console.log('\n✅ 已添加的功能開關:');
      results.addedToggles.forEach(item => {
        console.log(`  - ${item.name}: 預設啟用`);
      });
    }
    
    if (results.errors.length > 0) {
      console.log('\n❌ 錯誤:');
      results.errors.forEach(error => console.log(`  - ${error}`));
    }

    console.log('\n💡 說明:');
    console.log('  - 所有新添加的功能開關預設為 "true"（啟用）');
    console.log('  - 可以在 APIKEY 工作表中手動調整開關狀態');
    console.log('  - KNOWLEDGE_BASE 功能開關已修復，不會再出現錯誤');

    return results;

  } catch (error) {
    console.error('❌ 修復功能開關失敗:', error);
    results.errors.push(`修復失敗: ${error.message}`);
    return results;
  }
}

/**
 * 🔍 檢查功能開關完整性
 * 不執行修復，只檢查和報告
 */
function 檢查功能開關完整性_debug() {
  console.log('🔍 ===== 檢查功能開關完整性 =====');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      throw new Error('找不到 APIKEY 工作表');
    }

    // 基於 core_feature_toggle.gs 的完整清單
    const expectedToggles = [
      'IMAGE_GENERATION', 'IMAGE_BUTTONS', 'IMAGE_STORY', 'IMAGE_STORY_CONTINUATION',
      'TEXT_TO_SPEECH', 'CONVERSATIONAL_AUDIO',
      'NOTE_TAKING', 'FILE_QUERY', 'CONVERSATION_REVIEW',
      'GROUP_MEMBER_QUERY', 'SMART_RESPONDER', 'PIGGYBACK_SYSTEM',
      'SOCIAL_MEDIA_POST', 'DRIVE_LINK_SHARING', 'KNOWLEDGE_BASE',
      'SYSTEM_HELP', 'MODEL_EXAMPLES', 'SMART_SEARCH',
      'EXPERIMENTAL_FEATURES'
    ];

    const data = apikeySheet.getDataRange().getValues();
    const foundToggles = [];
    const missingToggles = [];

    // 收集現有的功能開關
    for (let i = 0; i < data.length; i++) {
      const cellA = data[i][0];
      
      if (typeof cellA === 'string' && cellA.startsWith('功能開關_')) {
        const featureName = cellA.replace('功能開關_', '');
        foundToggles.push({
          name: featureName,
          row: i + 1,
          value: data[i][1]
        });
      }
    }

    // 檢查缺失的功能開關
    const foundNames = foundToggles.map(t => t.name);
    expectedToggles.forEach(toggle => {
      if (!foundNames.includes(toggle)) {
        missingToggles.push(toggle);
      }
    });

    console.log(`\n📋 發現 ${foundToggles.length} 個現有功能開關:`);
    foundToggles.forEach(toggle => {
      console.log(`  ✅ 功能開關_${toggle.name}: "${toggle.value}" (第${toggle.row}行)`);
    });

    if (missingToggles.length > 0) {
      console.log(`\n⚠️ 發現 ${missingToggles.length} 個缺失的功能開關:`);
      missingToggles.forEach(toggle => {
        console.log(`  ❌ 缺失: 功能開關_${toggle}`);
      });
      console.log('\n💡 執行 修復缺失功能開關_debug() 來自動修復');
    } else {
      console.log('\n✅ 所有功能開關都已存在，完整性檢查通過');
    }

    return {
      total: expectedToggles.length,
      found: foundToggles.length,
      missing: missingToggles.length,
      foundToggles: foundToggles,
      missingToggles: missingToggles
    };

  } catch (error) {
    console.error('❌ 檢查功能開關完整性失敗:', error);
    return { error: error.message };
  }
}
