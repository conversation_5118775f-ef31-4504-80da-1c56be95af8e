// 語音模型修復_debug.gs
// 🔧 修復語音模型配置問題

/**
 * 🔧 修復語音模型配置
 */
function 修復語音模型配置_debug() {
  console.log('🔧 開始修復語音模型配置...');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ 找不到 APIKEY 工作表');
      return;
    }
    
    // 檢查當前配置
    console.log('\n=== 當前配置檢查 ===');
    const currentTtsModel = apikeySheet.getRange('B18').getValue();
    const currentAudioDialogModel = apikeySheet.getRange('B19').getValue();
    
    console.log('當前 TTS 模型 (B18):', currentTtsModel);
    console.log('當前對話音頻模型 (B19):', currentAudioDialogModel);
    
    // 修復配置
    console.log('\n=== 修復配置 ===');
    
    // 修復 TTS 模型：使用 flash 版本避免配額問題
    const correctTtsModel = 'gemini-2.5-flash-preview-tts';
    if (currentTtsModel !== correctTtsModel) {
      apikeySheet.getRange('B18').setValue(correctTtsModel);
      console.log(`✅ TTS 模型已修復: ${currentTtsModel} → ${correctTtsModel}`);
    } else {
      console.log('✅ TTS 模型配置正確');
    }
    
    // 修復對話音頻模型：使用標準版本（非 thinking 版本）
    const correctAudioDialogModel = 'gemini-2.5-flash-preview-native-audio-dialog';
    if (currentAudioDialogModel !== correctAudioDialogModel) {
      apikeySheet.getRange('B19').setValue(correctAudioDialogModel);
      console.log(`✅ 對話音頻模型已修復: ${currentAudioDialogModel} → ${correctAudioDialogModel}`);
    } else {
      console.log('✅ 對話音頻模型配置正確');
    }
    
    // 驗證修復結果
    console.log('\n=== 修復後驗證 ===');
    const newConfig = getConfig();
    console.log('新 TTS 模型:', newConfig.ttsModel);
    console.log('新對話音頻模型:', newConfig.audioDialogModel);
    
    // 測試修復後的功能
    console.log('\n=== 測試修復後的功能 ===');
    
    // 測試對話音頻模型
    console.log('測試對話音頻模型...');
    const audioResult = callGeminiAudioDialog('測試修復後的語音功能', {});
    console.log('對話音頻測試結果:');
    console.log('- 成功:', audioResult.success);
    console.log('- 錯誤:', audioResult.error);
    console.log('- 使用模型:', audioResult.modelUsed);
    
    if (!audioResult.success) {
      console.log('❌ 對話音頻仍然失敗，嘗試 TTS 備用方案...');
      
      // 測試 TTS 模型
      const ttsResult = textToSpeechWithGemini('測試修復後的 TTS 功能');
      console.log('TTS 測試結果:');
      console.log('- 成功:', ttsResult.success);
      console.log('- 錯誤:', ttsResult.error);
      console.log('- 可播放:', ttsResult.isPlayable);
    }
    
    console.log('\n🔧 語音模型配置修復完成');
    
  } catch (error) {
    console.error('❌ 修復過程發生錯誤:', error);
    console.error('錯誤堆疊:', error.stack);
  }
}

/**
 * 🔍 檢查可用的語音模型
 */
function 檢查可用語音模型_debug() {
  console.log('🔍 檢查可用的語音模型...');
  
  try {
    const config = getConfig();
    
    // 測試不同的語音模型
    const modelsToTest = [
      'gemini-2.5-flash-preview-native-audio-dialog',
      'gemini-2.5-flash-exp-native-audio-thinking-dialog',
      'gemini-2.5-flash-preview-tts',
      'gemini-2.5-pro-preview-tts'
    ];
    
    console.log('\n=== 測試語音模型可用性 ===');
    
    for (const modelName of modelsToTest) {
      console.log(`\n測試模型: ${modelName}`);
      
      try {
        const apiVersion = getModelApiVersion(modelName);
        const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${config.geminiApiKey}`;
        
        const payload = JSON.stringify({
          contents: [{
            parts: [{ text: '測試' }]
          }],
          generationConfig: {
            responseModalities: ["AUDIO"],
            speechConfig: {
              voiceConfig: {
                prebuiltVoiceConfig: {
                  voiceName: 'Kore'
                }
              }
            }
          }
        });
        
        const response = UrlFetchApp.fetch(url, {
          method: 'POST',
          contentType: 'application/json',
          payload: payload,
          muteHttpExceptions: true
        });
        
        const responseCode = response.getResponseCode();
        const responseText = response.getContentText();
        
        if (responseCode === 200) {
          console.log(`✅ ${modelName}: 可用`);
        } else if (responseCode === 404) {
          console.log(`❌ ${modelName}: 不存在 (404)`);
        } else if (responseCode === 429) {
          console.log(`⚠️ ${modelName}: 配額耗盡 (429)`);
        } else {
          console.log(`❓ ${modelName}: 其他錯誤 (${responseCode})`);
        }
        
      } catch (error) {
        console.log(`❌ ${modelName}: 測試失敗 - ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ 檢查過程發生錯誤:', error);
  }
}

/**
 * 🔄 重置語音配置為預設值
 */
function 重置語音配置_debug() {
  console.log('🔄 重置語音配置為預設值...');
  
  try {
    const ss = SpreadsheetApp.getActiveSpreadsheet();
    const apikeySheet = ss.getSheetByName('APIKEY');
    
    if (!apikeySheet) {
      console.log('❌ 找不到 APIKEY 工作表');
      return;
    }
    
    // 設定為預設值
    apikeySheet.getRange('B18').setValue('gemini-2.5-flash-preview-tts');
    apikeySheet.getRange('B19').setValue('gemini-2.5-flash-preview-native-audio-dialog');
    
    console.log('✅ 語音配置已重置為預設值');
    console.log('- TTS 模型: gemini-2.5-flash-preview-tts');
    console.log('- 對話音頻模型: gemini-2.5-flash-preview-native-audio-dialog');
    
  } catch (error) {
    console.error('❌ 重置過程發生錯誤:', error);
  }
}
