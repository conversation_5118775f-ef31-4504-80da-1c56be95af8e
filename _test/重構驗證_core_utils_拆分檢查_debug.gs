/*
 * 檔案: 重構驗證_core_utils_拆分檢查_debug.gs
 * 分類: test
 * 功能開關: -
 * 描述: 驗證 core_utils.gs 重構拆分後的函數完整性
 * 依賴: [core_utils_*.gs]
 * 最後更新: 2025-07-11
 */

/**
 * 🧪 core_utils 重構驗證測試
 * 按照 SOP 指南進行重構後的函數完整性檢查
 */
function 重構驗證_core_utils_拆分檢查_debug() {
  console.log('🧪 ===== core_utils 重構驗證測試 =====');
  
  const results = {
    timestamp: new Date(),
    summary: {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0
    },
    tests: {
      functionExistence: [],
      functionCalls: [],
      moduleIntegrity: []
    },
    issues: [],
    recommendations: []
  };

  try {
    // ===== 🔍 測試1：檢查關鍵函數是否存在 =====
    console.log('\n🔍 測試1：檢查關鍵函數是否存在...');
    
    const keyFunctions = [
      // 配置管理函數
      { name: 'getConfig', module: 'core_utils_config.gs', critical: true },
      { name: 'getConfigValue', module: 'core_utils_config.gs', critical: true },
      { name: 'getCurrentGeminiApiKey', module: 'core_utils_config.gs', critical: true },
      
      // API 調用函數
      { name: 'callGemini', module: 'core_utils_api.gs', critical: true },
      { name: 'getModelForTask', module: 'core_utils_api.gs', critical: false },
      { name: 'callGeminiWithIntent', module: 'core_utils_api.gs', critical: false },
      
      // 日誌記錄函數
      { name: 'logActivity', module: 'core_utils_logging.gs', critical: true },
      { name: 'getFileTypeInfo', module: 'core_utils_logging.gs', critical: false },
      
      // 輔助函數
      { name: 'normalizeText', module: 'core_utils_helpers.gs', critical: false },
      { name: 'normalizeCommand', module: 'core_utils_helpers.gs', critical: false },
      { name: 'isFeatureToggleEnabled', module: 'core_utils_helpers.gs', critical: true },
      
      // 回覆函數
      { name: 'replyMessage', module: 'core_utils_reply.gs', critical: true },
      { name: 'replyWithAudio', module: 'core_utils_reply.gs', critical: true },
      { name: 'replyWithImage', module: 'core_utils_reply.gs', critical: true }
    ];

    keyFunctions.forEach(funcInfo => {
      results.summary.totalTests++;
      try {
        const func = eval(funcInfo.name);
        if (typeof func === 'function') {
          results.tests.functionExistence.push({
            function: funcInfo.name,
            module: funcInfo.module,
            status: 'PASS',
            critical: funcInfo.critical
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${funcInfo.name}: 存在且可調用`);
        } else {
          results.tests.functionExistence.push({
            function: funcInfo.name,
            module: funcInfo.module,
            status: 'FAIL',
            error: '不是函數',
            critical: funcInfo.critical
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${funcInfo.name}: 不是函數`);
          
          if (funcInfo.critical) {
            results.issues.push(`關鍵函數 ${funcInfo.name} 不存在或不可調用`);
          }
        }
      } catch (error) {
        results.tests.functionExistence.push({
          function: funcInfo.name,
          module: funcInfo.module,
          status: 'FAIL',
          error: error.message,
          critical: funcInfo.critical
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${funcInfo.name}: ${error.message}`);
        
        if (funcInfo.critical) {
          results.issues.push(`關鍵函數 ${funcInfo.name} 缺失: ${error.message}`);
        }
      }
    });

    // ===== 🔍 測試2：檢查函數調用是否正常 =====
    console.log('\n🔍 測試2：檢查函數調用是否正常...');
    
    const functionCallTests = [
      {
        name: 'getConfig 調用測試',
        test: () => {
          const config = getConfig();
          return config && typeof config === 'object' && config.lineChannelAccessToken !== undefined;
        }
      },
      {
        name: 'getConfigValue 調用測試',
        test: () => {
          const value = getConfigValue('lineChannelAccessToken');
          return value !== null;
        }
      },
      {
        name: 'logActivity 調用測試',
        test: () => {
          logActivity('Test', '重構驗證測試', 'Success', 'test', 'debug', '測試日誌記錄功能');
          return true;
        }
      }
    ];

    functionCallTests.forEach(testInfo => {
      results.summary.totalTests++;
      try {
        const result = testInfo.test();
        if (result) {
          results.tests.functionCalls.push({
            test: testInfo.name,
            status: 'PASS'
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${testInfo.name}: 通過`);
        } else {
          results.tests.functionCalls.push({
            test: testInfo.name,
            status: 'FAIL',
            error: '測試返回 false'
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${testInfo.name}: 測試返回 false`);
          results.issues.push(`函數調用測試失敗: ${testInfo.name}`);
        }
      } catch (error) {
        results.tests.functionCalls.push({
          test: testInfo.name,
          status: 'FAIL',
          error: error.message
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${testInfo.name}: ${error.message}`);
        results.issues.push(`函數調用測試錯誤: ${testInfo.name} - ${error.message}`);
      }
    });

    // ===== 🔍 測試3：檢查模組完整性 =====
    console.log('\n🔍 測試3：檢查模組完整性...');
    
    const moduleFiles = [
      'core_utils_config.gs',
      'core_utils_api.gs', 
      'core_utils_helpers.gs',
      'core_utils_reply.gs',
      'core_utils_logging.gs'
    ];

    moduleFiles.forEach(moduleName => {
      results.summary.totalTests++;
      try {
        // 檢查模組是否存在（通過檢查模組中的關鍵函數）
        let moduleExists = false;
        let keyFunction = '';
        
        switch (moduleName) {
          case 'core_utils_config.gs':
            keyFunction = 'getConfig';
            break;
          case 'core_utils_api.gs':
            keyFunction = 'callGemini';
            break;
          case 'core_utils_helpers.gs':
            keyFunction = 'normalizeText';
            break;
          case 'core_utils_reply.gs':
            keyFunction = 'replyMessage';
            break;
          case 'core_utils_logging.gs':
            keyFunction = 'logActivity';
            break;
        }
        
        try {
          const func = eval(keyFunction);
          moduleExists = typeof func === 'function';
        } catch (e) {
          moduleExists = false;
        }
        
        if (moduleExists) {
          results.tests.moduleIntegrity.push({
            module: moduleName,
            status: 'PASS',
            keyFunction: keyFunction
          });
          results.summary.passedTests++;
          console.log(`  ✅ ${moduleName}: 模組完整`);
        } else {
          results.tests.moduleIntegrity.push({
            module: moduleName,
            status: 'FAIL',
            error: `關鍵函數 ${keyFunction} 不存在`,
            keyFunction: keyFunction
          });
          results.summary.failedTests++;
          console.log(`  ❌ ${moduleName}: 模組缺失或不完整`);
          results.issues.push(`模組 ${moduleName} 缺失或不完整`);
        }
        
      } catch (error) {
        results.tests.moduleIntegrity.push({
          module: moduleName,
          status: 'FAIL',
          error: error.message
        });
        results.summary.failedTests++;
        console.log(`  ❌ ${moduleName}: ${error.message}`);
        results.issues.push(`模組檢查錯誤: ${moduleName} - ${error.message}`);
      }
    });

    // ===== 📊 生成測試報告 =====
    console.log('\n📊 ===== 測試報告 =====');
    console.log(`總測試數: ${results.summary.totalTests}`);
    console.log(`通過: ${results.summary.passedTests}`);
    console.log(`失敗: ${results.summary.failedTests}`);
    console.log(`成功率: ${((results.summary.passedTests / results.summary.totalTests) * 100).toFixed(1)}%`);

    if (results.issues.length > 0) {
      console.log('\n⚠️ 發現問題:');
      results.issues.forEach(issue => console.log(`  - ${issue}`));
      
      // 生成建議
      results.recommendations.push('檢查缺失的函數是否已正確遷移到對應模組');
      results.recommendations.push('確認所有模組檔案都已正確創建');
      results.recommendations.push('檢查函數名稱是否與原始代碼完全一致');
    } else {
      console.log('\n✅ 所有測試通過，重構成功！');
      results.recommendations.push('重構驗證通過，可以進行下一步測試');
    }

    return results;

  } catch (error) {
    console.error('❌ 重構驗證測試失敗:', error);
    results.issues.push(`測試執行失敗: ${error.message}`);
    return results;
  }
}
