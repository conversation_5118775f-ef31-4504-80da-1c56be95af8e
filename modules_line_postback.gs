/*
 * 檔案: modules_line_postback.gs
 * 分類: line
 * 功能開關: SYSTEM_HELP
 * 描述: LINE Postback 事件處理
 * 依賴: [core_utils.gs, modules_image_buttons.gs]
 * 最後更新: 2025-07-11
 */

// == LINE Postback 事件處理模組 ==
// 專門處理 LINE 互動按鈕的 Postback 事件

/**
 * 🆕 v1.4.0 處理 Postback 訊息（擴展版 - 支援 4 個互動按鈕）
 * 🔘 支援功能：💾收藏SEED、📊生成詳情、🎪延續故事、📤分享卡片
 * 🚨 包含完整錯誤處理和用戶通知機制
 */
function handlePostbackMessage(postback, replyToken, userId, sourceType, groupId = '', roomId = '') {
  try {
    const { data, params } = postback;
    console.log(`🔘 收到 Postback: 用戶=${userId}(${sourceType}), 數據=${data}`);
    logActivity('Webhook', 'Postback訊息', 'Success', 'postback', 'handlePostbackMessage', `用戶${userId}(${sourceType})觸發postback: ${data}`);
    
    // 解析 postback 數據（GAS 兼容版本）
    const urlParams = parsePostbackData(data);
    const action = urlParams.action;
    
    if (!action) {
      // 🔧 處理舊版 postback 數據格式
      return handleLegacyPostback(data, replyToken, userId, sourceType);
    }
    
    console.log(`🎯 Postback 動作: ${action}`);
    
    switch(action) {
      case 'show_details':
        // 📊 生成詳情功能（舊版）
        handleShowDetailsPostback(urlParams, replyToken, userId, sourceType);
        break;

      case 'continue_story':
        // 🎪 延續故事功能（舊版）
        handleContinueStoryPostback(urlParams, replyToken, userId, sourceType);
        break;

      // 🆕 v1.5.1 新按鈕處理
      case 'story_params':
      case 'show_story_params':
        // 📋 故事參數按鈕
        handleNewPostbackAction(action, urlParams, replyToken, userId, sourceType, groupId, roomId);
        break;

      case 'share_story':
        // 📤 分享故事按鈕
        handleNewPostbackAction(action, urlParams, replyToken, userId, sourceType, groupId, roomId);
        break;

      case 'story_continue':
        // 🎬 故事接龍按鈕
        handleNewPostbackAction(action, urlParams, replyToken, userId, sourceType);
        break;

      case 'resize_image':
        // 🔄 圖片尺寸調整按鈕
        handleResizePostback(urlParams, replyToken, userId, sourceType);
        break;

      default:
        console.log(`未知的 Postback 動作: ${action}`);
        logActivity('System', '未知Postback動作', 'Success', 'postback', 'handlePostbackMessage', `action: ${action}, 來自: ${userId}(${sourceType})`);
        
        if (replyToken) {
          const unknownResponse = `⚠️ 未知的按鈕功能: ${action}\n\n請嘗試重新生成圖片或聯繫管理員`;
          replyMessage(replyToken, unknownResponse);
        }
        break;
    }
    
  } catch (error) {
    console.error('❌ Postback 處理錯誤:', error);
    logActivity('System', 'Postback處理錯誤', 'Failure', 'postback', 'handlePostbackMessage', error.toString());
    
    if (replyToken) {
      const errorResponse = `❌ 按鈕功能處理失敗\n\n錯誤訊息: ${error.message}\n\n請稍後再試或重新生成圖片`;
      replyMessage(replyToken, errorResponse);
    }
  }
}

/**
 * 🆕 v1.5.1 處理新按鈕動作
 */
function handleNewPostbackAction(action, urlParams, replyToken, userId, sourceType, groupId = '', roomId = '') {
  try {
    console.log(`🆕 處理新按鈕動作: ${action}`);

    // 獲取數據 ID
    const dataId = urlParams.id;

    if (!dataId) {
      throw new Error(`缺少數據 ID: ${action}`);
    }

    // 從 PostbackDataManager 獲取完整數據
    const fullData = retrievePostbackData(dataId);

    if (!fullData) {
      throw new Error(`找不到數據: ${dataId}`);
    }

    // 構建目標信息
    const targetInfo = {
      userId: userId,
      sourceType: sourceType,
      groupId: sourceType === 'group' ? groupId : null,
      roomId: sourceType === 'room' ? roomId : null,
      replyToken: replyToken
    };

    // 根據動作類型調用對應的處理函數
    switch(action) {
      case 'story_params':
      case 'show_story_params':
        return handleStoryParamsButton(fullData, targetInfo);
      
      case 'share_story':
        return handleShareStoryButton(fullData, targetInfo);
      
      case 'story_continue':
        return handleStoryContinueButton(fullData, targetInfo);
      
      default:
        throw new Error(`未支援的新按鈕動作: ${action}`);
    }

  } catch (error) {
    console.error(`❌ 新按鈕動作處理失敗 (${action}):`, error);
    
    if (replyToken) {
      const errorMessage = `❌ ${action} 功能暫時無法使用\n\n${error.message}\n\n請稍後再試`;
      replyMessage(replyToken, errorMessage);
    }
  }
}

/**
 * 處理舊版詳情顯示 Postback
 */
function handleShowDetailsPostback(urlParams, replyToken, userId, sourceType) {
  try {
    console.log('📊 處理生成詳情 Postback');
    
    const { seed, model, prompt } = urlParams;
    
    if (!seed || !model || !prompt) {
      throw new Error('缺少必要參數');
    }
    
    const detailsText = `📊 圖片生成詳情\n\n🎲 SEED: ${seed}\n🤖 模型: ${model}\n📝 提示詞: ${decodeURIComponent(prompt)}\n\n💡 您可以使用相同的 SEED 重新生成類似圖片`;
    
    if (replyToken) {
      replyMessage(replyToken, detailsText);
    }
    
  } catch (error) {
    console.error('❌ 詳情顯示處理失敗:', error);
    
    if (replyToken) {
      const errorMessage = `❌ 無法顯示生成詳情\n\n${error.message}`;
      replyMessage(replyToken, errorMessage);
    }
  }
}

/**
 * 處理舊版故事延續 Postback
 */
function handleContinueStoryPostback(urlParams, replyToken, userId, sourceType) {
  try {
    console.log('🎪 處理延續故事 Postback');
    
    // 檢查是否有新版數據 ID
    const dataId = urlParams.id;
    
    if (dataId) {
      // 新版：使用 PostbackDataManager
      console.log(`✅ [v1.6.1] 使用新版 PostbackDataManager 處理延續故事`);

      const fullData = retrievePostbackData(dataId);
      if (!fullData || !fullData.additionalData) {
        throw new Error('缺少必要的故事參數');
      }

      const targetInfo = {
        userId: userId,
        sourceType: sourceType,
        replyToken: replyToken
      };

      // 🔧 v1.6.1 修復：確保數據格式正確
      const storyContinueData = {
        seed: fullData.seed,
        prompt: fullData.prompt || fullData.additionalData.character_base || '未知描述'
      };

      console.log(`🔧 [v1.6.1] 故事接龍數據: SEED=${storyContinueData.seed}, 提示詞=${storyContinueData.prompt}`);

      // 調用統一的按鈕處理函數
      return handleStoryContinueButton(storyContinueData, targetInfo);

    } else {
      // 舊版：直接從 URL 參數獲取
      console.log(`🔄 [v1.6.1] 使用舊版 URL 參數處理延續故事`);
      
      const { seed, character_base } = urlParams;
      
      if (!seed || !character_base) {
        throw new Error('缺少必要的故事參數 (seed 或 character_base)');
      }
      
      const storyContinueData = {
        seed: seed,
        prompt: decodeURIComponent(character_base)
      };
      
      const targetInfo = {
        userId: userId,
        sourceType: sourceType,
        replyToken: replyToken
      };
      
      return handleStoryContinueButton(storyContinueData, targetInfo);
    }
    
  } catch (error) {
    console.error('❌ 延續故事處理失敗:', error);
    
    if (replyToken) {
      const errorMessage = `❌ 故事延續功能暫時無法使用\n\n${error.message}\n\n請稍後再試或重新生成圖片`;
      replyMessage(replyToken, errorMessage);
    }
  }
}

/**
 * 處理圖片尺寸調整 Postback
 */
function handleResizePostback(urlParams, replyToken, userId, sourceType) {
  try {
    console.log('🔄 處理圖片尺寸調整 Postback');
    
    const { dataId, size } = urlParams;
    
    if (!dataId || !size) {
      throw new Error('缺少必要參數');
    }
    
    // 從 PostbackDataManager 獲取完整數據
    const fullData = retrievePostbackData(dataId);
    
    if (!fullData) {
      throw new Error(`找不到數據: ${dataId}`);
    }
    
    const targetInfo = {
      userId: userId,
      sourceType: sourceType,
      replyToken: replyToken
    };
    
    // 調用圖片尺寸調整處理函數
    return handleResizeButton({ ...fullData, newSize: size }, targetInfo);
    
  } catch (error) {
    console.error('❌ 圖片尺寸調整處理失敗:', error);
    
    if (replyToken) {
      const errorMessage = `❌ 圖片尺寸調整功能暫時無法使用\n\n${error.message}`;
      replyMessage(replyToken, errorMessage);
    }
  }
}

/**
 * 🔧 處理簡單 postback 格式
 */
function handleLegacyPostback(data, replyToken, userId, sourceType) {
  try {
    console.log(`🔄 處理簡單 Postback: ${data}`);
    
    switch(data) {
      case 'help_menu':
        const helpText = generateHelpMessage(sourceType);
        if (replyToken) replyMessage(replyToken, helpText);
        break;
        
      case 'file_analysis':
        if (replyToken) replyMessage(replyToken, '🔍 請上傳檔案或提供 Google Drive 連結進行分析');
        break;
        
      default:
        logActivity('System', '未知Postback', 'Success', 'postback', 'handleLegacyPostback', `data: ${data}, 來自: ${userId}(${sourceType})`);

        if (replyToken) {
          const unknownResponse = `⚠️ 未知的按鈕功能: ${data}\n\n請嘗試重新操作或聯繫管理員`;
          replyMessage(replyToken, unknownResponse);
        }
        break;
    }
    
  } catch (error) {
    console.error('❌ 簡單 Postback 處理錯誤:', error);
    
    if (replyToken) {
      const errorResponse = `❌ 按鈕功能處理失敗: ${error.message}`;
      replyMessage(replyToken, errorResponse);
    }
  }
}

/**
 * 🔧 解析 Postback 數據（GAS 兼容版本）
 * 替代 URLSearchParams，因為 GAS 不支持該 API
 */
function parsePostbackData(data) {
  try {
    const params = {};

    if (!data || typeof data !== 'string') {
      return params;
    }

    // 分割參數
    const pairs = data.split('&');

    for (const pair of pairs) {
      const [key, value] = pair.split('=');
      if (key && value !== undefined) {
        // URL 解碼
        params[decodeURIComponent(key)] = decodeURIComponent(value);
      }
    }

    console.log(`🔧 解析 Postback 數據: ${JSON.stringify(params)}`);
    return params;

  } catch (error) {
    console.error('❌ 解析 Postback 數據失敗:', error);
    return {};
  }
}
