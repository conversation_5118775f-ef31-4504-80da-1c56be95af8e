/*
 * 檔案: modules_line_message_router.gs
 * 分類: line
 * 功能開關: SYSTEM_HELP
 * 描述: LINE 訊息路由和分發邏輯
 * 依賴: [core_utils.gs, modules_line_processor_core.gs]
 * 最後更新: 2025-07-11
 */

// == LINE 訊息路由和分發邏輯 ==
// 專門處理不同類型訊息的路由分發

// ===== 訊息類型路由器 =====

/**
 * 訊息類型路由器
 */
function routeMessageByType(messageType, event, replyToken, userId, sourceType) {
  try {
    switch(messageType) {
      case 'text':
        // 🚀 使用 AI-First 系統處理文字訊息
        try {
          return handleTextMessageAIFirst(event.message.text, replyToken, userId, sourceType);
        } catch (aiError) {
          console.error('AI-First 路由處理失敗，使用備用系統:', aiError);
          return handleTextMessage(event.message.text, replyToken, userId, sourceType);
        }
      
      case 'image':
      case 'video':
      case 'audio':
      case 'file':
        return handleMediaMessage(event.message, replyToken, userId, sourceType);
      
      case 'location':
        return handleLocationMessage(event.message, replyToken, userId, sourceType);
      
      case 'sticker':
        return handleStickerMessage(event.message, replyToken, userId, sourceType);
      
      default:
        console.log(`未處理的訊息類型: ${messageType}`);
        logActivity('Webhook', '未知訊息類型', 'Success', messageType, 'routeMessageByType', `用戶${userId}(${sourceType})發送了未知類型訊息: ${messageType}`);
        return false;
    }
  } catch (error) {
    console.error(`訊息路由錯誤 (${messageType}):`, error);
    logActivity('System', '訊息路由錯誤', 'Failure', messageType, 'routeMessageByType', error.toString());
    return false;
  }
}

// ===== 特定訊息類型處理器 =====

/**
 * 處理位置訊息
 */
function handleLocationMessage(message, replyToken, userId, sourceType) {
  try {
    const { title, address, latitude, longitude } = message;
    
    // 記錄位置資訊
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('地點日誌');
    if (sheet) {
      const timestamp = new Date();
      sheet.appendRow([timestamp, userId, title || '未知位置', longitude, latitude, address || '']);
    }

    logActivity('Webhook', '位置訊息', 'Success', 'location', 'handleLocationMessage', `用戶${userId}(${sourceType})分享位置: ${title || address}`);

    // 只在個人對話中回應位置訊息
    if (replyToken && sourceType === 'user') {
      const responseText = `📍 已收到您的位置資訊！\n\n🏷️ 位置：${title || '未知位置'}\n📬 地址：${address || '未提供地址'}\n🗺️ 座標：${latitude}, ${longitude}\n\n💡 您可以說「推薦附近的美食」來獲得建議`;
      replyMessage(replyToken, responseText);
    }
    
    return true;
  } catch (error) {
    console.error('處理位置訊息錯誤:', error);
    logActivity('System', '位置訊息錯誤', 'Failure', 'location', 'handleLocationMessage', error.toString());
    return false;
  }
}

/**
 * 處理貼圖訊息
 */
function handleStickerMessage(message, replyToken, userId, sourceType) {
  try {
    const { packageId, stickerId } = message;
    
    logActivity('Webhook', '貼圖訊息', 'Success', 'sticker', 'handleStickerMessage', `用戶${userId}(${sourceType})發送貼圖: ${packageId}/${stickerId}`);
    
    // 只在個人對話中回應貼圖
    if (replyToken && sourceType === 'user') {
      // 隨機回應一些友善的貼圖
      const friendlyStickers = [
        { packageId: '446', stickerId: '1988' }, // 熊大點頭
        { packageId: '446', stickerId: '1989' }, // 兔兔揮手
        { packageId: '446', stickerId: '2027' }  // 莎莉微笑
      ];
      
      const randomSticker = friendlyStickers[Math.floor(Math.random() * friendlyStickers.length)];
      
      const stickerMessage = {
        type: 'sticker',
        packageId: randomSticker.packageId,
        stickerId: randomSticker.stickerId
      };
      
      replyMessage(replyToken, stickerMessage);
    }
    
    return true;
  } catch (error) {
    console.error('處理貼圖訊息錯誤:', error);
    logActivity('System', '貼圖訊息錯誤', 'Failure', 'sticker', 'handleStickerMessage', error.toString());
    return false;
  }
}

/**
 * 處理媒體訊息（圖片、影片、音頻、檔案）
 */
function handleMediaMessage(message, replyToken, userId, sourceType) {
  try {
    const { type, id } = message;
    
    logActivity('Webhook', '媒體訊息', 'Success', type, 'handleMediaMessage', `用戶${userId}(${sourceType})發送${type}訊息: ${id}`);
    
    // 根據媒體類型進行不同處理
    switch(type) {
      case 'image':
        return handleImageMessage(message, replyToken, userId, sourceType);
      
      case 'video':
        return handleVideoMessage(message, replyToken, userId, sourceType);
      
      case 'audio':
        return handleAudioMessage(message, replyToken, userId, sourceType);
      
      case 'file':
        return handleFileMessage(message, replyToken, userId, sourceType);
      
      default:
        console.log(`未處理的媒體類型: ${type}`);
        return false;
    }
    
  } catch (error) {
    console.error('處理媒體訊息錯誤:', error);
    logActivity('System', '媒體訊息錯誤', 'Failure', message.type, 'handleMediaMessage', error.toString());
    return false;
  }
}

/**
 * 處理圖片訊息
 */
function handleImageMessage(message, replyToken, userId, sourceType) {
  try {
    // 只在個人對話中處理圖片
    if (sourceType === 'user' && replyToken) {
      const responseText = `📸 已收到您的圖片！\n\n💡 您可以：\n• 說「分析這張圖片」來獲得AI分析\n• 說「!畫圖 類似風格的圖片」來生成相似圖片\n• 上傳更多圖片進行比較分析`;
      replyMessage(replyToken, responseText);
    }
    
    return true;
  } catch (error) {
    console.error('處理圖片訊息錯誤:', error);
    return false;
  }
}

/**
 * 處理影片訊息
 */
function handleVideoMessage(message, replyToken, userId, sourceType) {
  try {
    // 只在個人對話中處理影片
    if (sourceType === 'user' && replyToken) {
      const responseText = `🎬 已收到您的影片！\n\n💡 您可以說「分析這個影片」來獲得內容分析`;
      replyMessage(replyToken, responseText);
    }
    
    return true;
  } catch (error) {
    console.error('處理影片訊息錯誤:', error);
    return false;
  }
}

/**
 * 處理音頻訊息
 */
function handleAudioMessage(message, replyToken, userId, sourceType) {
  try {
    // 只在個人對話中處理音頻
    if (sourceType === 'user' && replyToken) {
      const responseText = `🎵 已收到您的音頻！\n\n💡 您可以說「轉錄這段音頻」來獲得文字轉錄`;
      replyMessage(replyToken, responseText);
    }
    
    return true;
  } catch (error) {
    console.error('處理音頻訊息錯誤:', error);
    return false;
  }
}

/**
 * 處理檔案訊息
 */
function handleFileMessage(message, replyToken, userId, sourceType) {
  try {
    // 只在個人對話中處理檔案
    if (sourceType === 'user' && replyToken) {
      const responseText = `📄 已收到您的檔案！\n\n💡 您可以：\n• 說「分析這個檔案」來獲得內容分析\n• 說「總結檔案內容」來獲得摘要\n• 提供 Google Drive 連結以獲得更好的處理效果`;
      replyMessage(replyToken, responseText);
    }
    
    return true;
  } catch (error) {
    console.error('處理檔案訊息錯誤:', error);
    return false;
  }
}

// ===== 輔助函數 =====

/**
 * 檢查是否有驚嘆號前綴
 */
function hasExclamationPrefix(text) {
  return text && text.trim().startsWith('!');
}

/**
 * 正規化文字
 */
function normalizeText(text) {
  if (!text) return '';
  return text.trim().toLowerCase();
}

/**
 * 生成幫助訊息
 */
function generateHelpMessage(sourceType) {
  const baseHelp = `🤖 LINE Bot 智能助理\n\n✨ AI-First 設計，自然對話即可使用所有功能\n\n🎯 主要功能：\n• 💬 智能對話 - 直接說話即可\n• 🎨 圖片生成 - 說「畫一張...」\n• 🔊 語音合成 - 說「唸出...」\n• 📝 筆記管理 - 說「記住...」\n• 📁 檔案分析 - 上傳檔案或提供連結`;
  
  if (sourceType === 'group') {
    return baseHelp + `\n\n👥 群組模式：\n• 使用 ! 開頭可確保回應\n• 系統會智能判斷何時回應\n• 支援群組成員查詢功能`;
  }
  
  return baseHelp + `\n\n💡 提示：直接說出您的需求，AI 會自動理解並執行相應功能！`;
}
