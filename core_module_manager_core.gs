/*
 * 檔案: core_module_manager_core.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組管理核心功能
 * 依賴: [core_module_mapping.gs, core_utils_config.gs]
 * 最後更新: 2025-07-11
 */

// == 模組管理核心功能 ==
// 提供模組啟用/停用的核心邏輯

/**
 * 🔄 自動模組管理主函數
 * 根據試算表設定自動啟用/停用功能模組
 */
function autoManageModules() {
  try {
    console.log('🔄 開始自動模組管理...');
    
    const results = {};
    
    // 遍歷所有功能模組
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      console.log(`🔍 檢查功能: ${featureName} (${moduleInfo.description})`);
      
      // 檢查試算表中的開關狀態
      const isEnabled = isFeatureEnabled(featureName);
      console.log(`📊 ${featureName} 開關狀態: ${isEnabled ? '啟用' : '停用'}`);
      
      if (isEnabled) {
        // 功能啟用：確保檔案在根目錄
        results[featureName] = activateModule(featureName, moduleInfo);
      } else {
        // 功能停用：將檔案移到 _sleeping
        results[featureName] = deactivateModule(featureName, moduleInfo);
      }
    }
    
    console.log('✅ 自動模組管理完成');
    return results;
    
  } catch (error) {
    console.error('❌ 自動模組管理失敗:', error);
    return { error: error.message };
  }
}

/**
 * ✅ 啟用模組：將檔案從 _sleeping 移動到根目錄
 */
function activateModule(featureName, moduleInfo) {
  const results = {
    feature: featureName,
    action: 'activate',
    files: {},
    success: true,
    errors: []
  };

  try {
    console.log(`✅ 啟用模組: ${featureName}`);
    
    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
      console.warn(`⚠️ 功能 ${featureName} 的檔案清單無效或缺失`);
      results.success = false;
      results.errors.push(`功能 ${featureName} 的檔案清單無效或缺失`);
      return results;
    }
    
    for (const fileName of moduleInfo.files) {
      try {
        const moveResult = moveFileToRoot(fileName);
        results.files[fileName] = moveResult;
        
        if (!moveResult.success) {
          results.success = false;
          results.errors.push(`${fileName}: ${moveResult.message}`);
        }
        
      } catch (fileError) {
        console.error(`❌ 處理檔案 ${fileName} 時發生錯誤:`, fileError);
        results.files[fileName] = { success: false, message: fileError.message };
        results.success = false;
        results.errors.push(`${fileName}: ${fileError.message}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 啟用模組 ${featureName} 失敗:`, error);
    results.success = false;
    results.errors.push(error.message);
  }

  return results;
}

/**
 * ❌ 停用模組：將檔案從根目錄移動到 _sleeping
 */
function deactivateModule(featureName, moduleInfo) {
  const results = {
    feature: featureName,
    action: 'deactivate',
    files: {},
    success: true,
    errors: []
  };

  try {
    console.log(`❌ 停用模組: ${featureName}`);
    
    // 安全檢查：確保 moduleInfo.files 存在且是陣列
    if (!moduleInfo.files || !Array.isArray(moduleInfo.files)) {
      console.warn(`⚠️ 功能 ${featureName} 的檔案清單無效或缺失`);
      results.success = false;
      results.errors.push(`功能 ${featureName} 的檔案清單無效或缺失`);
      return results;
    }
    
    for (const fileName of moduleInfo.files) {
      try {
        const moveResult = moveFileToSleeping(fileName);
        results.files[fileName] = moveResult;
        
        if (!moveResult.success) {
          results.success = false;
          results.errors.push(`${fileName}: ${moveResult.message}`);
        }
        
      } catch (fileError) {
        console.error(`❌ 處理檔案 ${fileName} 時發生錯誤:`, fileError);
        results.files[fileName] = { success: false, message: fileError.message };
        results.success = false;
        results.errors.push(`${fileName}: ${fileError.message}`);
      }
    }
    
  } catch (error) {
    console.error(`❌ 停用模組 ${featureName} 失敗:`, error);
    results.success = false;
    results.errors.push(error.message);
  }

  return results;
}

/**
 * 🔍 檢查功能是否啟用
 * @param {string} featureName - 功能名稱
 * @returns {boolean} 是否啟用
 */
function isFeatureEnabled(featureName) {
  try {
    // 使用 isFeatureToggleEnabled 函數檢查功能開關
    return isFeatureToggleEnabled(featureName);
  } catch (error) {
    console.error(`❌ 檢查功能開關失敗: ${featureName}`, error);
    return false; // 預設為停用
  }
}

/**
 * 📊 獲取所有模組狀態
 * @returns {Object} 所有模組的狀態資訊
 */
function getAllModuleStatus() {
  const status = {};
  
  try {
    for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
      status[featureName] = {
        enabled: isFeatureEnabled(featureName),
        description: moduleInfo.description,
        fileCount: moduleInfo.files ? moduleInfo.files.length : 0,
        files: moduleInfo.files || []
      };
    }
  } catch (error) {
    console.error('❌ 獲取模組狀態失敗:', error);
  }
  
  return status;
}

/**
 * 🔄 重新載入模組配置
 * 清除快取並重新讀取配置
 */
function reloadModuleConfiguration() {
  try {
    console.log('🔄 重新載入模組配置...');
    
    // 清除 PropertiesService 中的快取
    const properties = PropertiesService.getScriptProperties();
    const keys = properties.getKeys();
    
    for (const key of keys) {
      if (key.startsWith('module_cache_') || key.startsWith('file_location_')) {
        properties.deleteProperty(key);
      }
    }
    
    console.log('✅ 模組配置已重新載入');
    return { success: true, message: '模組配置已重新載入' };
    
  } catch (error) {
    console.error('❌ 重新載入模組配置失敗:', error);
    return { success: false, message: error.message };
  }
}

/**
 * 🎯 批次啟用/停用模組
 * @param {Array<string>} featureNames - 功能名稱陣列
 * @param {boolean} enable - true 為啟用，false 為停用
 * @returns {Object} 批次操作結果
 */
function batchToggleModules(featureNames, enable) {
  const results = {
    action: enable ? 'batch_enable' : 'batch_disable',
    total: featureNames.length,
    success: 0,
    failed: 0,
    details: {}
  };
  
  try {
    for (const featureName of featureNames) {
      const moduleInfo = getModuleInfo(featureName);
      
      if (!moduleInfo) {
        results.details[featureName] = {
          success: false,
          message: '功能不存在'
        };
        results.failed++;
        continue;
      }
      
      try {
        const result = enable 
          ? activateModule(featureName, moduleInfo)
          : deactivateModule(featureName, moduleInfo);
          
        results.details[featureName] = result;
        
        if (result.success) {
          results.success++;
        } else {
          results.failed++;
        }
        
      } catch (error) {
        results.details[featureName] = {
          success: false,
          message: error.message
        };
        results.failed++;
      }
    }
    
  } catch (error) {
    console.error('❌ 批次操作失敗:', error);
    results.error = error.message;
  }
  
  return results;
}
