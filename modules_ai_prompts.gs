/*
 * 檔案: modules_ai_prompts.gs
 * 分類: ai
 * 功能開關: -
 * 描述: 統一提示詞管理系統 + 角色設定深度整合 - 重複利用角色設定工作表
 * 依賴: [modules_piggyback_system.gs, core_utils.gs, core_model_config.gs]
 * 最後更新: 2025-07-10 - 修復圖像提示詞翻譯準確性問題
 */

// == 統一提示詞管理系統 ==
// 🤖 集中管理所有 AI 提示詞，實現真正的 AI First 體驗
// 🎯 重點：自然聊天口氣，避免 AI 味和八股文
// 🚀 v2.3 - 深度整合角色設定系統，重複利用角色設定工作表

/**
 * 🧠 提示詞分類管理
 * 按功能分組，統一風格和調用方式
 */
const PROMPTS = {
  
  // === 意圖理解類 ===
  INTENT_ANALYSIS: {
    template: `你是個聰明的助手，幫我理解用戶想要什麼。

用戶說：「{userMessage}」
環境：{sourceType}

🎯 **關鍵字優先識別規則**：
1. 🎨 **圖像生成** - 如果包含以下任一關鍵字，必須識別為 image_generation：
   - 「畫」、「畫一」、「畫個」、「畫張」
   - 「圖片」、「圖像」、「圖」
   - 「生成圖」、「創建圖」、「製作圖」
   - 「給我...圖」、「幫我畫」
   - 範例：「畫貓咪」、「畫一張貓咪的圖」、「給我水果圖片」

2. 🔊 **TTS語音功能** - 如果包含以下關鍵字：
   - text_to_speech：「tts」、「你說」、「你念」、「念出來」、「讀出來」、「語音」、「播放」、「說出來」
   - conversational_audio：「語音聊天」、「語音對話」、「跟我聊」、「聊聊天」、「聊聊」、「聊天」、「用語音」、「對話」

3. 📝 **其他功能**：
   - 記錄筆記：「記下」、「記住」、「筆記」
   - 查檔案：「剛才那個」、「之前的文件」
   - 查群組成員：「XXX都說了什麼」、「XXX有提到」、「總結XXX」
   - 系統指令：「!測試」、「help」、「功能」、「狀態」

⚠️ **重要**：如果用戶說「畫貓咪」，這明顯是要生成貓咪的圖片，必須識別為 image_generation，不能識別為其他意圖！

請用 JSON 回答，primary_intent 必須是以下其中一個：
- note_taking (記錄筆記)
- file_query (查檔案)
- conversation_review (看聊天記錄)
- group_member_query (查群組成員發言)
- social_media_post (發社群貼文)
- text_to_speech (文字轉語音)
- conversational_audio (對話音頻)
- image_generation (生成圖片) ⭐ 包含「畫」、「圖片」、「生成圖」等關鍵字
- semantic_search (語義搜索)
- batch_processing (批量處理)
- system_command (系統指令)
- model_examples (模型範例)
- general_question (一般問題)
- casual_chat (閒聊)
- system_help (需要幫助)
- drive_link_sharing (分享連結)

{
  "primary_intent": "從上面列表選一個",
  "confidence": 信心度(0-100),
  "key_entities": ["重要的人名或關鍵字"],
  "natural_language_summary": "用白話說用戶想幹嘛",
  "suggested_action": "建議怎麼處理"
}`,

    params: ['userMessage', 'sourceType']
  },

  // === 🤔 智能引導類 - 新增 ===
  
  // 統一智能引導模板
  UNIFIED_GUIDANCE: {
    template: `🤔 我不太確定您的需求，我可以幫您：

🔧 **系統功能**
• 「測試」- 檢查系統狀態
• 「help」- 完整功能說明  
• 「範例」- 使用示範

📝 **AI助理** (直接說話即可)
• 記錄筆記：「記住明天開會」
• 查詢對話：「我們聊了什麼」
• 群組查詢：「Michael說了什麼」  
• 語音功能：「你說今天很棒」
• 圖片生成：「畫一隻貓」

💡 直接告訴我您想做什麼，我會理解您的意圖！`,
    
    params: []
  },

  // === 🔊 TTS 和音頻處理類 ===
  
  // TTS 模式分析（判斷鸚鵡模式 vs 對話模式）
  TTS_MODE_ANALYSIS: {
    template: `分析用戶想要什麼類型的語音功能：

用戶說：「{message}」

有兩種模式：
🦜 鸚鵡模式：用戶明確要求複述特定文字
  - 關鍵字：「tts」、「你說」、「重複」、「複述」、「念出來」、「讀出來」
  - 範例：「tts 今天天氣很好」、「你說 我愛你」、「重複這句話」

🎙️ 對話模式：用戶想要智能對話回應並轉成語音
  - 關鍵字：「聊天」、「聊聊」、「語音聊天」
  - 範例：「跟我聊聊天」、「今天天氣如何？」、「你好嗎？」、「語音聊天」、「用語音跟我說話」

請用 JSON 回答：
{
  "mode": "parrot" 或 "conversational",
  "textToSpeak": "如果是鸚鵡模式，要說的具體文字",
  "userQuery": "如果是對話模式，用戶的問題",
  "confidence": 信心度(0-100)
}`,

    params: ['message']
  },

  // TTS 文字提取
  TTS_TEXT_EXTRACTION: {
    template: `從用戶的話中提取要轉換成語音的文字：

用戶說：「{message}」

🎯 智能提取規則：
- 如果有「tts」、「你說」、「念出來」等指令詞，提取後面的內容
- 支援各種格式：「你說：XXX」、「你說 XXX」、「你說"XXX"」、「你說XXX」
- 自動去除指令詞和標點符號（冒號、引號、括號等）
- 如果沒有明確內容，返回原始訊息
- 保持文字的自然性，不要改變語義

範例：
- 「你說：我想吃酸菜魚」→「我想吃酸菜魚」
- 「念出來"今天天氣很好"」→「今天天氣很好」
- 「語音播放 Hello World」→「Hello World」

要轉換的文字：`,

    params: ['message']
  },

  // === 🎨 圖像生成類 ===
  
  // 圖像提示詞提取
  IMAGE_PROMPT_EXTRACTION: {
    template: `從用戶的話中提取圖像生成的描述：

用戶說：「{message}」

要求：
1. 移除指令詞：「畫」「畫一張」「畫一個」「畫圖」「生成」「創建」「給我」等
2. 保留所有關鍵視覺元素，不能遺漏核心描述對象
3. 控制在 200 字符以內
4. 不要解釋過程或提供用戶選項

全英文 AI 文生圖提示詞：`,

    params: ['message']
  },

  // 圖像提示詞增強
  IMAGE_PROMPT_ENHANCEMENT: {
    template: `幫我改成適合用AI文生圖的提示詞：

原始描述：「{originalPrompt}」

🎯 翻譯和優化要求：
• 如果不是純英文，必須翻譯成英文，特別注意保留所有關鍵視覺元素，不能遺漏核心描述對象！
• 可以添加藝術風格、光線效果、構圖描述等增強細節
• 使用專業的攝影和藝術術語提升圖像質量
• 長度限制：生成的英文提示詞必須控制在 400 個字符以內，如果過長則摘要, 但必須保留所有關鍵視覺元素，沒有過長就如實翻譯。

優化後的全英文 AI 文生圖提示詞：`,

    params: ['originalPrompt']
  },

  // Reply成本節省圖片提示詞生成（智能版）
  REPLY_COST_SAVER_IMAGE_PROMPT_GENERATION: {
    template: `你是專業的圖片提示詞生成器。根據以下角色設定和情境，直接生成一個完整的英文圖片提示詞。

角色資料：
• 基礎描述：{characterDescription.基礎描述}
• 角色名稱：{characterDescription.角色名稱}
• 年齡：{characterDescription.年齡}歲
• 性別：{characterDescription.性別}
• 職業：{characterDescription.職業}
• 個性：{characterDescription.個性}
• 聊天風格：{characterDescription.聊天風格}
• 時段情境：{characterDescription.時段情境}

時段：{timeRange}
用戶訊息：{originalMessage}

要求：
1. 直接輸出完整的英文圖片提示詞，不要任何說明文字
2. 整合所有角色特質（年齡、性別、職業、個性）
3. 融入時段情境
4. 保持自然攝影風格
5. 控制在400字符以內
6. 不要包含任何中文或解釋

直接輸出英文提示詞：`,

    params: ['characterDescription', 'timeRange', 'originalMessage']
  },

  // === 📊 語義搜索類 ===
  
  // 語義搜索回應生成
  SEMANTIC_SEARCH_RESPONSE: {
    template: `用戶查詢：「{query}」

我找到了 {resultCount} 個相關結果：{topResults}

請用自然聊天的口氣回應：
• 告訴用戶找到了相關內容
• 簡單說明結果的相關性
• 語氣要友善自然
• 可以建議下一步操作

回應：`,

    params: ['query', 'resultCount', 'topResults']
  },

  // === 查詢解析類 ===
  QUERY_PARSING: {
    template: `幫我分析用戶的查詢需求：

用戶問：「{userQuery}」

我需要知道：
• 想查誰的發言？（用戶名稱）
• 要看多少筆？（數量限制，預設50筆）
• 是要簡單列表還是深度分析？

範例：
• "Michael最近500筆對話" → 要500筆
• "總結蚵仔煎的發言" → 預設50筆，簡單總結
• "你覺得Andrew講的有道理嗎？" → 要分析，不只是列表
• "給我看所有蚵大的發言" → 全部

用 JSON 回答：
{
  "targetUser": "目標用戶名稱",
  "recordLimit": 數量或"all",
  "queryType": "summary"或"analysis",
  "isValidQuery": true/false,
  "analysisPrompt": "如果是分析類型，原始問題是什麼"
}`,
    
    params: ['userQuery']
  },

  // === 用戶映射類 ===
  USER_MAPPING: {
    template: `幫我建立群組用戶的昵稱對應表，讓大家更容易查詢：

用戶列表：
{userList}

請考慮：
• 昵稱和簡稱（Michael = Mike = 麥可）
• 去掉特殊符號（⚡Michael（台灣智能） = Michael）
• 常見縮寫（Andrew = Andy）
• 中英文對應

用 JSON 格式回答：
{
  "映射關鍵字": "完整顯示名稱",
  "另一個關鍵字": "對應的完整顯示名稱"
}

例如：
{
  "michael": "⚡Michael（台灣智能）",
  "mike": "⚡Michael（台灣智能）",
  "蚵仔煎": "蚵仔煎/雲林AI第一人",
  "蚵大": "蚵仔煎/雲林AI第一人"
}`,
    
    params: ['userList']
  },

  // === 用戶名稱解析 ===
  USER_NAME_RESOLVE: {
    template: `用戶想查詢「{queryUserName}」，幫我找出是指哪個群組成員：

可用的群組用戶：
{availableUsers}

請判斷用戶想查詢哪一個具體用戶。考慮昵稱、簡稱、特殊符號等。

如果找到匹配用戶，請只回答完整的顯示名稱。
如果沒有匹配用戶，請回答 "NONE"。

匹配用戶：`,
    
    params: ['queryUserName', 'availableUsers']
  },

  // === 回答生成類 ===
  
  // 簡潔查詢回答（針對「有提到...嗎？」類型問題）
  SIMPLE_QUERY_RESPONSE: {
    template: `用戶問：「{originalQuery}」

這是關於「{targetUser}」的發言記錄：
{messageContext}

這是個簡單的查詢問題，用戶只想要直接的答案。

請用自然聊天的口氣回答：
• 如果有提到，直接說「有」，然後簡單說在哪裡提到
• 如果沒提到，直接說「沒有」或「我沒看到」
• 不要寫長篇分析報告
• 語氣要像朋友聊天一樣自然

回答：`,
    
    params: ['originalQuery', 'targetUser', 'messageContext']
  },

  // 用戶發言總結
  USER_SUMMARY: {
    template: `幫我總結「{targetUser}」最近的發言：

發言記錄（共{totalCount}筆）：
{messageContext}

用自然的口氣總結：
• 主要在聊什麼話題
• 有什麼特別的觀點或想法
• 發言風格如何

不要寫得像報告，就像朋友之間聊天時說「他最近都在聊...」那樣自然。
控制在200字左右。

總結：`,
    
    params: ['targetUser', 'totalCount', 'messageContext']
  },

  // 用戶發言分析（深度問題）
  USER_ANALYSIS: {
    template: `用戶問：「{analysisPrompt}」

這是「{targetUser}」的發言記錄：
{messageContext}

請用聊天的口氣回答用戶的問題：
• 直接回應用戶問的問題
• 可以引用具體的發言內容
• 保持客觀但不要太正式
• 就像朋友間討論一樣自然

回答：`,
    
    params: ['analysisPrompt', 'targetUser', 'messageContext']
  },

  // === 內容提取類 ===
  
  // 筆記內容提取
  NOTE_EXTRACTION: {
    template: `從用戶的話中提取要記錄的內容：

用戶說：「{message}」

請只回傳需要記錄的核心內容，不要包含「記錄」、「記住」這些動作詞。
如果不確定要記什麼，就回傳空字符串。

要記錄的內容：`,
    
    params: ['message']
  },

  // 筆記確認回應
  NOTE_CONFIRMATION: {
    template: `用戶想記錄：「{noteContent}」

請用自然聊天的口氣確認已經記錄了：
• 語氣要友善自然，像朋友間對話
• 確認記錄的內容
• 可以加點關心或建議，但不要太多
• 50字以內

確認回應：`,
    
    params: ['noteContent']
  },

  // === 閒聊回應類 ===
  CASUAL_CHAT: {
    template: `用戶說：「{originalMessage}」

請直接回應用戶，就像朋友間的對話：
• 🚫 絕對不要自稱智能助理、AI助手、機器人或類似詞彙
• 用第一人稱回應（我、我在、我覺得等）
• 語氣要友善親切，像朋友聊天
• 不要提供建議或選項，直接回答
• 不要太正式或有 AI 味
• 簡潔一點，30字以內

直接回應：`,

    params: ['originalMessage']
  },

  // === 🔧 系統指令類 ===

  // 系統指令識別
  SYSTEM_COMMAND_ANALYSIS: {
    template: `分析用戶的系統指令請求：

用戶說：「{userMessage}」

判斷用戶想要執行什麼系統功能：
🔧 測試 - 系統狀態檢查
📚 範例 - 功能使用範例
❓ 幫助 - 完整功能說明
📊 狀態 - 系統健康檢查

請用 JSON 回答：
{
  "command_type": "test|examples|help|status",
  "confidence": 信心度(0-100),
  "user_intent": "用戶想要什麼"
}`,

    params: ['userMessage']
  },

  // 模型範例生成
  MODEL_EXAMPLES_RESPONSE: {
    template: `生成 AI 功能使用範例指南：

用戶想了解：{requestType}

請生成包含以下功能的使用範例：
🔊 語音功能（TTS）
🎨 圖片生成
📊 語義搜索
⚡ 快速處理
📝 筆記功能
🧠 對話記憶

每個功能提供 2-3 個具體範例，語氣要自然友善。

範例指南：`,

    params: ['requestType']
  },

  // === 幫助說明類 ===
  HELP_RESPONSE: {
    template: `為智能 LINE Bot 生成使用說明：

環境：{environmentType}
AI 模型：{modelInfo}

用自然的口氣說明：
• 這是個 AI 助手，不需要記命令，直接說話就行
• 主要功能：記筆記、分析檔案、查聊天記錄{groupFeatures}
• 🔊 語音功能：說「念出來」或「語音播放」可以把文字轉成語音，說「跟我聊聊」可以語音對話
• 🎨 圖像生成：說「畫一張圖」或「生成圖片」可以創作圖像
• 📊 智能搜索：說「找相關檔案」可以進行語義搜索
• ⚡ 快速處理：說「批量處理」或「快速整理」可以高效完成任務
• 可以直接上傳檔案讓 AI 分析
• 就像跟朋友聊天一樣，AI 會懂你的意思

語氣要輕鬆友善，不要寫得像說明書。

使用說明：`,

    params: ['environmentType', 'modelInfo', 'groupFeatures']
  },

  // === 🎭 CHARACTER_PROFILES 整合類 - 新增 ===

  // 個性化提示詞生成器
  PERSONALIZED_PROMPT_BUILDER: {
    template: `根據人設特色生成個性化提示詞：

基礎提示詞：{basePrompt}
人設資料：
• 身份：{characterName}（{age}歲{occupation}）
• 個性：{personality}
• 聊天風格：{chatStyle}
• 回應長度：{responseLength}
• 正式程度：{formalityLevel}
• 表情符號：{emojiUsage}
• 禁用詞彙：{forbiddenPhrases}

請生成符合人設特色的個性化提示詞：
• 完全按照人設回應，避免使用禁用詞彙
• 體現個性和聊天風格
• 控制回應長度和正式程度
• 適當使用表情符號

個性化提示詞：`,

    params: ['basePrompt', 'characterName', 'age', 'occupation', 'personality', 'chatStyle', 'responseLength', 'formalityLevel', 'emojiUsage', 'forbiddenPhrases']
  },

  // 創意寫作（人設版）
  CREATIVE_WRITING_PERSONALIZED: {
    template: `根據以下要求創作內容：

寫作提示：{prompt}
寫作風格：{style}

🎭 人設指導：
• 身份：{characterName}（{age}歲{occupation}）
• 個性：{personality}
• 聊天風格：{chatStyle}
• 禁用詞彙：{forbiddenPhrases}
• 表情符號：{emojiUsage}級別

要求：
• 用繁體中文創作
• 完全符合人設特色和個性
• 內容要有創意和吸引力
• 嚴格避免使用禁用詞彙
• 體現人設的聊天風格

創作內容：`,

    params: ['prompt', 'style', 'characterName', 'age', 'occupation', 'personality', 'chatStyle', 'forbiddenPhrases', 'emojiUsage']
  },

  // 群組智能分析（人設版）- v2.3 加強群組靜默判斷
  GROUP_ANALYSIS_PERSONALIZED: {
    template: `作為群組觀察者，分析這個對話場景：

對話內容：{conversation}
群組氛圍：{groupAtmosphere}

🎭 當前人設：{characterName}（{characterBehavior}）
• 個性：{personality}
• 群組行為：{groupBehavior}
• 禁用詞彙：{forbiddenPhrases}

🎯 請嚴格判斷是否應該參與這個對話：

✅ 明確適合回應的情況（必須滿足其中一項）：
- 直接提及：明確提到「{characterName}」或直接向你提問
- 女性話題：討論女性相關、感情、美容、時尚等話題
- 心情分享：有人分享心情、感受、生活感悟
- 需要幫助：明確請求協助或支持
- 情感支持：有人明顯需要鼓勵、安慰或關懷

❌ 應該保持靜默的情況（群組靜默）：
- 新聞討論：政治、時事、新聞相關話題
- 技術討論：工作、技術、專業領域討論
- 男性話題：運動、遊戲、車子等偏男性興趣話題
- 簡短對話：純粹問候、簡短回應、表情符號
- 私人對話：兩人之間的私人討論
- 商務對話：工作安排、會議討論等
- 沒有明確提及你的一般聊天

🔍 嚴格標準：
- 只有在明確符合「適合回應」條件時才回應
- 有疑慮時選擇保持靜默
- 絕對避免使用：{forbiddenPhrases}
- 記住：寧可錯過回應機會，也不要打擾不相關的對話

請用 JSON 格式回答：
{
  "should_respond": true/false,
  "confidence": 0-100,
  "reason": "判斷理由",
  "response_motivation": "如果要回應，動機是什麼"
}`,

    params: ['conversation', 'groupAtmosphere', 'characterName', 'characterBehavior', 'personality', 'groupBehavior', 'forbiddenPhrases']
  },

  // 智能回應生成（人設版）
  SMART_RESPONSE_PERSONALIZED: {
    template: `用戶說：「{userMessage}」

🎭 請以{characterName}的身份回應：
• 年齡職業：{age}歲{occupation}
• 個性特色：{personality}
• 聊天風格：{chatStyle}
• 字數控制：{lengthGuidance}
• 場景類型：{detectedScenario}
• 正式程度：{formalityLevel}
• 表情符號：{emojiUsage}級別
• 嚴格禁用：{forbiddenPhrases}

回應要求：
• 完全符合人設特色
• 用第一人稱回應（我、我在、我覺得等）
• 語氣親切自然，像朋友聊天
• 不要太正式或有 AI 味
• 嚴格避免使用禁用詞彙
• 嚴格遵守字數控制指引

回應：`,

    params: ['userMessage', 'characterName', 'age', 'occupation', 'personality', 'chatStyle', 'lengthGuidance', 'detectedScenario', 'formalityLevel', 'emojiUsage', 'forbiddenPhrases']
  },

  // 🎭 角色名稱提及回應
  CHARACTER_MENTION_RESPONSE: {
    template: `你是 {characterName}，一個 {age} 歲的 {occupation}。

個性特質：{personality}
聊天風格：{chatStyle}
正式程度：{formalityLevel}
表情符號使用：{emojiUsage}

有人在群組中提到了你的名字：
「{userMessage}」

請以 {characterName} 的身份自然回應：

回應指引：
• 用第一人稱回應（我、我在、我覺得等）
• 表現出被提到時的自然反應
• 語氣親切自然，像朋友聊天
• 不要太正式或有 AI 味
• 回應長度控制在 20-40 字
• 嚴格避免使用禁用詞彙：{forbiddenPhrases}

回應：`,

    params: ['userMessage', 'mentionedName', 'characterName', 'age', 'occupation', 'personality', 'chatStyle', 'formalityLevel', 'emojiUsage', 'forbiddenPhrases']
  }
};

/**
 * 🎯 統一的提示詞調用接口
 * 取代分散在各處的提示詞硬編碼
 * 🎭 支援 CHARACTER_PROFILES 整合
 */
function callAIWithPrompt(promptType, context = {}, parameters = {}) {
  try {
    if (!PROMPTS[promptType]) {
      throw new Error(`未知的提示詞類型: ${promptType}`);
    }

    const promptConfig = PROMPTS[promptType];

    // 🎭 自動整合 CHARACTER_PROFILES（如果是個性化提示詞）
    let finalContext = { ...context };
    if (isPersonalizedPrompt(promptType)) {
      const activeProfile = getActiveCharacterProfile();
      if (activeProfile) {
        finalContext = { ...finalContext, ...activeProfile };
        console.log(`🎭 整合角色: ${activeProfile.characterName}`);
      }
    }

    // 🎯 智能場景檢測和字數控制
    const userMessage = finalContext.userMessage || finalContext.conversation || '';
    const intentType = finalContext.intentType || promptType;
    const sourceType = finalContext.sourceType || 'private';
    const responseType = finalContext.responseType || 'text';

    const detectedScenario = detectResponseScenario(userMessage, intentType, sourceType);
    const lengthGuidance = getResponseLengthGuidance(detectedScenario, responseType);

    // 將字數指引加入上下文
    finalContext.lengthGuidance = lengthGuidance;
    finalContext.detectedScenario = detectedScenario;

    console.log(`🎯 智能場景檢測: ${detectedScenario}, 字數指引: ${lengthGuidance}`);

    const prompt = buildPrompt(promptConfig.template, finalContext, parameters);

    console.log(`🤖 使用提示詞: ${promptType}`);

    // 🚀 根據提示詞類型智能選擇模型
    const taskHint = getTaskHintFromPromptType(promptType);
    return callGemini(prompt, 'general', null, taskHint);

  } catch (error) {
    console.error(`提示詞調用失敗 (${promptType}):`, error);
    throw error;
  }
}

/**
 * 🎭 判斷是否為個性化提示詞
 * 需要整合 CHARACTER_PROFILES 的提示詞類型
 */
function isPersonalizedPrompt(promptType) {
  const personalizedPrompts = [
    'PERSONALIZED_PROMPT_BUILDER',
    'CREATIVE_WRITING_PERSONALIZED',
    'GROUP_ANALYSIS_PERSONALIZED',
    'SMART_RESPONSE_PERSONALIZED',
    'CHARACTER_MENTION_RESPONSE'
  ];

  return personalizedPrompts.includes(promptType);
}

/**
 * 🧠 根據提示詞類型獲取任務提示
 * 讓系統自動選擇最適合的模型
 */
function getTaskHintFromPromptType(promptType) {
  const taskMapping = {
    // 語音相關
    'TTS_MODE_ANALYSIS': 'general',
    'TTS_TEXT_EXTRACTION': 'general',

    // 圖像相關
    'IMAGE_PROMPT_EXTRACTION': 'general',
    'IMAGE_PROMPT_ENHANCEMENT': 'general',

    // 語義搜索相關
    'SEMANTIC_SEARCH_RESPONSE': 'fast_processing', // 使用快速模型回應搜索結果

    // 智能引導相關
    'UNIFIED_GUIDANCE': 'fast_processing', // 智能引導使用快速模型

    // CHARACTER_PROFILES 個性化相關 - 新增
    'PERSONALIZED_PROMPT_BUILDER': 'general',
    'CREATIVE_WRITING_PERSONALIZED': 'general',
    'GROUP_ANALYSIS_PERSONALIZED': 'general',
    'SMART_RESPONSE_PERSONALIZED': 'general',

    // 一般處理
    'INTENT_ANALYSIS': 'general',
    'CASUAL_CHAT': 'general',
    'NOTE_CONFIRMATION': 'fast_processing', // 簡單確認使用快速模型
    'HELP_RESPONSE': 'general'
  };

  return taskMapping[promptType] || 'general';
}

/**
 * 🔧 提示詞模板建構器
 * 支持參數替換和上下文注入
 */
function buildPrompt(template, context = {}, parameters = {}) {
  let prompt = template;

  // 替換模板中的參數
  const allParams = { ...context, ...parameters };

  for (const [key, value] of Object.entries(allParams)) {
    const placeholder = `{${key}}`;
    // 🔧 修復：確保 value 是字符串，避免 undefined.replace 錯誤
    const safeValue = (value !== null && value !== undefined) ? String(value) : '';
    prompt = prompt.replace(new RegExp(placeholder.replace(/[{}]/g, '\\$&'), 'g'), safeValue);
  }

  return prompt;
}

/**
 * 🎯 智能回答路由器
 * 根據問題類型選擇合適的回答策略
 */
function getResponseStrategy(userQuery, queryType = 'unknown') {
  // 簡單查詢（是否問題）
  if (/有提到|有說|有講|提過|說過/.test(userQuery) && /嗎\?*$/.test(userQuery)) {
    return 'SIMPLE_QUERY_RESPONSE';
  }
  
  // 分析類問題
  if (/你覺得|怎麼看|有道理|分析|評價/.test(userQuery)) {
    return 'USER_ANALYSIS';
  }
  
  // 預設總結
  return 'USER_SUMMARY';
}

// ===== 🎭 角色設定深度整合系統 - v2.3 新增 =====
// 🎯 讓 modules_ai_prompts.gs 也重複利用角色設定工作表
// 🔄 與 modules_piggyback_system.gs 使用相同的角色設定存取邏輯

/**
 * 🎭 專門為提示詞優化的角色獲取函數
 * 重複利用 modules_piggyback_system.gs 的角色設定邏輯
 */
function getCharacterProfileForPrompt() {
  try {
    // 🔄 重複利用現有的角色設定獲取邏輯
    const activeProfile = getActiveCharacterProfile();

    if (!activeProfile) {
      console.log('⚠️ 無啟用角色設定，使用預設值');
      return getDefaultCharacterProfile();
    }

    // 🎯 為提示詞系統優化角色設定
    const optimizedProfile = {
      ...activeProfile,

      // 🎭 提示詞專用欄位
      promptPersonality: activeProfile.personality || '友善且有幫助',
      promptStyle: activeProfile.chatStyle || '自然聊天，親切友善',
      promptTone: activeProfile.formalityLevel || 'casual',
      promptLength: activeProfile.responseLength || 'medium',
      promptEmoji: activeProfile.emojiUsage || 'moderate',

      // 🚫 安全過濾
      safePersonality: filterUnsafeContent(activeProfile.personality || ''),
      safeForbiddenPhrases: (activeProfile.forbiddenPhrases || '').split(',').map(p => p.trim()).filter(p => p)
    };

    console.log(`🎭 為提示詞優化角色: ${optimizedProfile.characterName}`);
    return optimizedProfile;

  } catch (error) {
    console.error('❌ 角色設定獲取失敗:', error);
    return getDefaultCharacterProfile();
  }
}

/**
 * 🔍 角色設定完整性驗證
 * 確保角色設定包含提示詞系統所需的所有欄位
 */
function validateCharacterProfile(profile) {
  if (!profile) {
    console.log('❌ 角色設定為空');
    return false;
  }

  const requiredFields = [
    'characterName',
    'age',
    'occupation',
    'personality',
    'chatStyle',
    'responseLength',
    'formalityLevel',
    'emojiUsage',
    'forbiddenPhrases'
  ];

  const missingFields = requiredFields.filter(field => !profile[field]);

  if (missingFields.length > 0) {
    console.log(`⚠️ 角色設定缺少欄位: ${missingFields.join(', ')}`);
    return false;
  }

  console.log(`✅ 角色設定驗證通過: ${profile.characterName}`);
  return true;
}

/**
 * 🎨 動態個性化提示詞生成
 * 根據角色設定動態調整提示詞內容
 * 🔧 v2.4 - 整合智能字數控制系統
 */
function buildPersonalizedPrompt(basePrompt, customProfile = null, scenario = 'default', responseType = 'text') {
  try {
    const profile = customProfile || getCharacterProfileForPrompt();

    if (!validateCharacterProfile(profile)) {
      console.log('⚠️ 角色設定驗證失敗，使用基礎提示詞');
      return basePrompt;
    }

    // 🎭 根據角色特色調整提示詞
    let personalizedPrompt = basePrompt;

    // 根據個性調整語氣
    if (profile.personality.includes('活潑') || profile.personality.includes('開朗')) {
      personalizedPrompt += '\n\n🎯 回應風格：用活潑開朗的語氣回應，展現積極正面的態度。';
    } else if (profile.personality.includes('溫和') || profile.personality.includes('親切')) {
      personalizedPrompt += '\n\n🎯 回應風格：用溫和親切的語氣回應，讓人感到溫暖。';
    } else if (profile.personality.includes('專業') || profile.personality.includes('認真')) {
      personalizedPrompt += '\n\n🎯 回應風格：用專業認真的語氣回應，提供可靠的協助。';
    }

    // 根據正式程度調整
    if (profile.formalityLevel === 'formal') {
      personalizedPrompt += '\n\n📝 語言風格：使用較正式的用詞，保持禮貌和專業。';
    } else if (profile.formalityLevel === 'casual') {
      personalizedPrompt += '\n\n📝 語言風格：使用輕鬆自然的用詞，像朋友聊天一樣。';
    }

    // 🎯 智能字數控制 - 根據場景動態調整
    const lengthGuidance = getResponseLengthGuidance(scenario, responseType);
    personalizedPrompt += `\n\n📏 回應長度指引：${lengthGuidance}`;

    console.log(`🎯 場景: ${scenario}, 類型: ${responseType}, 字數指引: ${lengthGuidance}`);

    // 表情符號使用指引
    if (profile.emojiUsage === 'heavy') {
      personalizedPrompt += '\n\n😊 表情符號：適當使用表情符號增加親切感。';
    } else if (profile.emojiUsage === 'none') {
      personalizedPrompt += '\n\n📝 表情符號：避免使用表情符號，保持文字簡潔。';
    }

    // 禁用詞彙提醒
    if (profile.safeForbiddenPhrases && profile.safeForbiddenPhrases.length > 0) {
      personalizedPrompt += `\n\n🚫 嚴格禁用：${profile.safeForbiddenPhrases.join('、')}`;
    }

    // 角色身份提醒
    personalizedPrompt += `\n\n🎭 請以${profile.characterName}的身份回應（${profile.age}歲${profile.occupation}）。`;

    console.log(`🎨 生成個性化提示詞: ${profile.characterName}`);
    return personalizedPrompt;

  } catch (error) {
    console.error('❌ 個性化提示詞生成失敗:', error);
    return basePrompt;
  }
}

/**
 * 👥 群聊場景專用角色設定
 * 重複利用角色設定工作表，針對群聊場景優化
 */
function getGroupChatProfile() {
  try {
    const baseProfile = getCharacterProfileForPrompt();

    if (!baseProfile) {
      return null;
    }

    // 🎯 群聊場景特化
    const groupProfile = {
      ...baseProfile,

      // 群聊專用行為設定
      scenarioType: 'group_chat',
      behaviorMode: baseProfile.groupBehavior || '適度參與群組討論',
      responseStrategy: 'selective', // 選擇性回應
      mentionSensitivity: 'high', // 高敏感度檢測提及

      // 群聊語氣調整
      groupTone: baseProfile.formalityLevel === 'formal' ? 'polite_group' : 'friendly_group',
      groupLength: 'medium', // 群聊中保持中等長度

      // 群聊特殊規則
      avoidInterruption: true, // 避免打斷對話
      respectPrivacy: true, // 尊重私人話題

      // 提及檢測關鍵字
      mentionKeywords: [
        baseProfile.characterName,
        baseProfile.characterName.toLowerCase(),
        // 可以添加昵稱或簡稱
      ]
    };

    console.log(`👥 群聊場景角色設定: ${groupProfile.characterName}`);
    return groupProfile;

  } catch (error) {
    console.error('❌ 群聊角色設定獲取失敗:', error);
    return null;
  }
}

/**
 * 💬 私聊場景專用角色設定
 * 重複利用角色設定工作表，針對私聊場景優化
 */
function getPrivateChatProfile() {
  try {
    const baseProfile = getCharacterProfileForPrompt();

    if (!baseProfile) {
      return null;
    }

    // 🎯 私聊場景特化
    const privateProfile = {
      ...baseProfile,

      // 私聊專用行為設定
      scenarioType: 'private_chat',
      behaviorMode: baseProfile.privateBehavior || '友善回應私人訊息',
      responseStrategy: 'active', // 積極回應
      personalAttention: 'high', // 高度個人關注

      // 私聊語氣調整
      privateTone: 'personal_friendly',
      privateLength: baseProfile.responseLength || 'medium',

      // 私聊特殊規則
      morePersonal: true, // 更個人化
      detailedResponse: true, // 更詳細回應
      emotionalSupport: true, // 提供情感支持

      // 私聊偏好話題
      preferredTopics: (baseProfile.preferredTopics || '').split(',').map(t => t.trim()).filter(t => t),
      avoidTopics: (baseProfile.avoidTopics || '').split(',').map(t => t.trim()).filter(t => t)
    };

    console.log(`💬 私聊場景角色設定: ${privateProfile.characterName}`);
    return privateProfile;

  } catch (error) {
    console.error('❌ 私聊角色設定獲取失敗:', error);
    return null;
  }
}

/**
 * 🎨 創作場景專用角色設定
 * 重複利用角色設定工作表，針對創作場景優化
 */
function getCreativeProfile() {
  try {
    const baseProfile = getCharacterProfileForPrompt();

    if (!baseProfile) {
      return null;
    }

    // 🎯 創作場景特化
    const creativeProfile = {
      ...baseProfile,

      // 創作專用行為設定
      scenarioType: 'creative',
      behaviorMode: 'creative_assistant',
      responseStrategy: 'inspirational', // 啟發性回應
      creativityLevel: 'high', // 高創意度

      // 創作語氣調整
      creativeTone: 'imaginative',
      creativeLength: 'detailed', // 創作時提供詳細內容

      // 創作特殊規則
      encourageCreativity: true, // 鼓勵創意
      provideInspiration: true, // 提供靈感
      supportExploration: true, // 支持探索

      // 創作相關提示詞
      baseImagePrompt: baseProfile.base_prompt || '',
      fixedSeed: baseProfile.fixed_seed || null,

      // 創作風格偏好
      creativeStyle: baseProfile.chatStyle || '富有想像力',
      artisticPersonality: baseProfile.personality + '，富有創意且有想像力'
    };

    console.log(`🎨 創作場景角色設定: ${creativeProfile.characterName}`);
    return creativeProfile;

  } catch (error) {
    console.error('❌ 創作角色設定獲取失敗:', error);
    return null;
  }
}

// ===== 🚀 角色設定快取機制 =====
// 🎯 提升性能，減少重複讀取工作表

/**
 * 📦 角色設定快取存儲
 * 使用 PropertiesService 進行會話級快取
 */
const CHARACTER_CACHE_KEY = 'character_profile_cache';
const CACHE_EXPIRY_KEY = 'character_cache_expiry';
const CACHE_DURATION = 5 * 60 * 1000; // 5分鐘快取

/**
 * 💾 快取角色設定
 * 將角色設定存儲到快取中，提升後續存取性能
 */
function cacheCharacterProfile(profile) {
  try {
    if (!profile) {
      console.log('⚠️ 無角色設定可快取');
      return false;
    }

    const cacheData = {
      profile: profile,
      timestamp: Date.now(),
      characterName: profile.characterName
    };

    const properties = PropertiesService.getScriptProperties();
    properties.setProperty(CHARACTER_CACHE_KEY, JSON.stringify(cacheData));
    properties.setProperty(CACHE_EXPIRY_KEY, (Date.now() + CACHE_DURATION).toString());

    console.log(`💾 角色設定已快取: ${profile.characterName}`);
    return true;

  } catch (error) {
    console.error('❌ 角色設定快取失敗:', error);
    return false;
  }
}

/**
 * 🔄 重新整理角色設定快取
 * 清除舊快取，重新獲取最新角色設定
 */
function refreshCharacterCache() {
  try {
    console.log('🔄 重新整理角色設定快取...');

    // 清除舊快取
    const properties = PropertiesService.getScriptProperties();
    properties.deleteProperty(CHARACTER_CACHE_KEY);
    properties.deleteProperty(CACHE_EXPIRY_KEY);

    // 重新獲取並快取
    const freshProfile = getActiveCharacterProfile();
    if (freshProfile) {
      cacheCharacterProfile(freshProfile);
      console.log(`✅ 角色設定快取已更新: ${freshProfile.characterName}`);
      return freshProfile;
    } else {
      console.log('⚠️ 無法獲取新的角色設定');
      return null;
    }

  } catch (error) {
    console.error('❌ 角色設定快取更新失敗:', error);
    return null;
  }
}

/**
 * 📖 獲取快取的角色設定
 * 優先使用快取，快取過期或不存在時重新獲取
 */
function getCachedProfile() {
  try {
    const properties = PropertiesService.getScriptProperties();
    const cacheData = properties.getProperty(CHARACTER_CACHE_KEY);
    const cacheExpiry = properties.getProperty(CACHE_EXPIRY_KEY);

    // 檢查快取是否存在且未過期
    if (cacheData && cacheExpiry) {
      const expiryTime = parseInt(cacheExpiry);
      const currentTime = Date.now();

      if (currentTime < expiryTime) {
        const parsedData = JSON.parse(cacheData);
        console.log(`📖 使用快取角色設定: ${parsedData.characterName}`);
        return parsedData.profile;
      } else {
        console.log('⏰ 角色設定快取已過期');
      }
    }

    // 快取不存在或已過期，重新獲取
    console.log('🔄 快取無效，重新獲取角色設定...');
    return refreshCharacterCache();

  } catch (error) {
    console.error('❌ 快取角色設定獲取失敗:', error);
    // 快取失敗時直接獲取
    return getActiveCharacterProfile();
  }
}

// ===== 🛠️ 輔助函數 =====

/**
 * 🛡️ 內容安全過濾
 * 過濾角色設定中的不安全內容
 */
function filterUnsafeContent(content) {
  if (!content || typeof content !== 'string') {
    return '';
  }

  // 基本安全過濾
  const unsafePatterns = [
    /\b(暴力|仇恨|歧視)\b/g,
    /\b(不當|不雅|粗俗)\b/g
  ];

  let filteredContent = content;
  unsafePatterns.forEach(pattern => {
    filteredContent = filteredContent.replace(pattern, '[已過濾]');
  });

  return filteredContent;
}

/**
 * 🎭 預設角色設定
 * 當無法獲取角色設定時使用的預設值
 */
function getDefaultCharacterProfile() {
  return {
    characterName: 'AI助手',
    age: 25,
    occupation: '智能助手',
    personality: '友善且有幫助',
    chatStyle: '自然聊天，親切友善',
    responseLength: 'medium',
    formalityLevel: 'casual',
    emojiUsage: 'moderate',
    groupBehavior: '適度參與群組討論',
    privateBehavior: '友善回應私人訊息',
    forbiddenPhrases: '智能助理,AI助手,機器人',
    preferredTopics: '一般聊天,生活分享',
    avoidTopics: '爭議話題,負面內容',

    // 提示詞專用欄位
    promptPersonality: '友善且有幫助',
    promptStyle: '自然聊天，親切友善',
    promptTone: 'casual',
    promptLength: 'medium',
    promptEmoji: 'moderate',
    safePersonality: '友善且有幫助',
    safeForbiddenPhrases: ['智能助理', 'AI助手', '機器人']
  };
}

// ===== 🔗 統一角色設定API =====
// 🎯 提供統一的角色設定存取介面，確保兩個模組使用相同方式

/**
 * 🎯 統一角色設定存取介面
 * 整合快取機制和錯誤處理，提供可靠的角色設定存取
 */
function getUnifiedCharacterProfile(useCache = true) {
  try {
    let profile;

    if (useCache) {
      // 優先使用快取
      profile = getCachedProfile();
    } else {
      // 直接從工作表獲取
      profile = getActiveCharacterProfile();
      if (profile) {
        cacheCharacterProfile(profile);
      }
    }

    // 驗證角色設定
    if (!validateCharacterProfile(profile)) {
      console.log('⚠️ 角色設定驗證失敗，使用預設設定');
      profile = getDefaultCharacterProfile();
    }

    return profile;

  } catch (error) {
    console.error('❌ 統一角色設定存取失敗:', error);
    return getDefaultCharacterProfile();
  }
}

/**
 * 🔄 統一角色設定更新介面
 * 當角色設定變更時，同步更新快取
 */
function updateUnifiedCharacterProfile(profileName) {
  try {
    console.log(`🔄 更新統一角色設定: ${profileName}`);

    // 使用 modules_piggyback_system.gs 的設定函數
    const result = setActiveCharacterProfile(profileName);

    if (result.success) {
      // 更新成功，重新整理快取
      refreshCharacterCache();
      console.log(`✅ 角色設定已更新並同步快取: ${profileName}`);
      return true;
    } else {
      console.log(`❌ 角色設定更新失敗: ${result.error}`);
      return false;
    }

  } catch (error) {
    console.error('❌ 統一角色設定更新失敗:', error);
    return false;
  }
}

/**
 * 📊 角色設定系統狀態檢查
 * 檢查兩個模組的角色設定整合狀態
 */
function checkCharacterProfileIntegration() {
  try {
    console.log('📊 === 角色設定系統整合狀態檢查 ===');

    const status = {
      timestamp: new Date().toISOString(),
      piggybankModule: null,
      promptModule: null,
      cacheStatus: null,
      integration: 'unknown'
    };

    // 檢查 modules_piggyback_system.gs 的角色設定
    try {
      status.piggybankModule = getActiveCharacterProfile();
      console.log(`✅ modules_piggyback_system: ${status.piggybankModule ? status.piggybankModule.characterName : '無'}`);
    } catch (error) {
      console.log(`❌ modules_piggyback_system: ${error.message}`);
    }

    // 檢查 modules_ai_prompts.gs 的角色設定
    try {
      status.promptModule = getCharacterProfileForPrompt();
      console.log(`✅ modules_ai_prompts: ${status.promptModule ? status.promptModule.characterName : '無'}`);
    } catch (error) {
      console.log(`❌ modules_ai_prompts: ${error.message}`);
    }

    // 檢查快取狀態
    try {
      status.cacheStatus = getCachedProfile();
      console.log(`✅ 快取狀態: ${status.cacheStatus ? status.cacheStatus.characterName : '無快取'}`);
    } catch (error) {
      console.log(`❌ 快取狀態: ${error.message}`);
    }

    // 判斷整合狀態
    if (status.piggybankModule && status.promptModule) {
      if (status.piggybankModule.characterName === status.promptModule.characterName) {
        status.integration = 'consistent';
        console.log('✅ 兩個模組角色設定一致');
      } else {
        status.integration = 'inconsistent';
        console.log('⚠️ 兩個模組角色設定不一致');
      }
    } else {
      status.integration = 'incomplete';
      console.log('❌ 角色設定整合不完整');
    }

    return status;

  } catch (error) {
    console.error('❌ 角色設定整合狀態檢查失敗:', error);
    return { integration: 'error', error: error.message };
  }
}
