/*
 * 檔案: core_module_mapping.gs
 * 分類: core
 * 功能開關: -
 * 描述: 模組檔案映射表定義
 * 依賴: []
 * 最後更新: 2025-07-11
 */

// == 模組檔案映射表定義 ==
// 定義每個功能對應的檔案前綴和檔案清單

/**
 * 🎛️ 功能模組檔案映射表
 * 定義每個功能對應的檔案前綴和檔案清單
 */
const MODULE_FILE_MAPPING = {
  IMAGE_GENERATION: {
    prefix: 'modules_image',
    files: [
      'modules_image_core.gs',
      'modules_image_push.gs',
      'modules_image_buttons.gs',
      'modules_image_utils.gs',
      'modules_image_generator.gs'
    ],
    description: '圖片生成功能模組'
  },

  TEXT_TO_SPEECH: {
    prefix: 'modules_audio',
    files: [
      'modules_audio_handler.gs',
      'modules_audio_advanced.gs'
    ],
    description: '音頻功能模組'
  },

  CONVERSATIONAL_AUDIO: {
    prefix: 'modules_audio',
    files: [
      'modules_audio_handler.gs',
      'modules_audio_advanced.gs'
    ],
    description: '對話音頻功能模組'
  },

  NOTE_TAKING: {
    prefix: 'modules_note',
    files: [
      'modules_note_memory.gs'
    ],
    description: '筆記管理功能模組'
  },

  FILE_QUERY: {
    prefix: 'modules_file',
    files: [
      'modules_file_extractor.gs',
      'modules_file_drive.gs'
    ],
    description: '檔案查詢功能模組'
  },

  CONVERSATION_REVIEW: {
    prefix: 'modules_note',
    files: [
      'modules_note_memory.gs'
    ],
    description: '對話回顧功能模組'
  },

  GROUP_MEMBER_QUERY: {
    prefix: 'modules_group',
    files: [
      'modules_group_tracker.gs'
    ],
    description: '群組成員查詢功能模組'
  },

  SOCIAL_MEDIA_POST: {
    prefix: 'modules_social',
    files: [
      // 功能實現在 AI 處理器中，暫時沒有專門檔案
    ],
    description: '社交媒體發文功能模組'
  },

  KNOWLEDGE_BASE: {
    prefix: 'modules_knowledge',
    files: [
      'modules_knowledge_base.gs'
    ],
    description: '知識庫功能模組'
  }

  // 🚫 已移除不必要的功能開關：
  // - LINE_WEBHOOK: 系統核心功能，永遠啟用
  // - AI_PROMPTS: 系統核心功能，永遠啟用
  // - GROUP_CHAT_TRACKING: 與 GROUP_MEMBER_QUERY 功能重複
};

/**
 * 🔍 獲取模組映射表
 * @returns {Object} 完整的模組檔案映射表
 */
function getModuleFileMapping() {
  return MODULE_FILE_MAPPING;
}

/**
 * 🔍 獲取特定功能的模組資訊
 * @param {string} featureName - 功能名稱
 * @returns {Object|null} 模組資訊或 null
 */
function getModuleInfo(featureName) {
  return MODULE_FILE_MAPPING[featureName] || null;
}

/**
 * 📋 獲取所有功能名稱列表
 * @returns {Array<string>} 功能名稱陣列
 */
function getAllFeatureNames() {
  return Object.keys(MODULE_FILE_MAPPING);
}

/**
 * 🔍 根據檔案名稱查找對應的功能
 * @param {string} fileName - 檔案名稱
 * @returns {Array<string>} 包含該檔案的功能名稱陣列
 */
function findFeaturesByFileName(fileName) {
  const features = [];
  
  for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
    if (moduleInfo.files && moduleInfo.files.includes(fileName)) {
      features.push(featureName);
    }
  }
  
  return features;
}

/**
 * 📊 獲取模組統計資訊
 * @returns {Object} 統計資訊
 */
function getModuleStatistics() {
  const stats = {
    totalFeatures: 0,
    totalFiles: 0,
    filesByPrefix: {},
    featuresWithoutFiles: []
  };
  
  for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
    stats.totalFeatures++;
    
    if (!moduleInfo.files || moduleInfo.files.length === 0) {
      stats.featuresWithoutFiles.push(featureName);
    } else {
      stats.totalFiles += moduleInfo.files.length;
      
      // 統計各前綴的檔案數量
      const prefix = moduleInfo.prefix;
      if (!stats.filesByPrefix[prefix]) {
        stats.filesByPrefix[prefix] = 0;
      }
      stats.filesByPrefix[prefix] += moduleInfo.files.length;
    }
  }
  
  return stats;
}

/**
 * 🔍 驗證模組映射表的完整性
 * @returns {Object} 驗證結果
 */
function validateModuleMapping() {
  const validation = {
    isValid: true,
    errors: [],
    warnings: []
  };
  
  for (const [featureName, moduleInfo] of Object.entries(MODULE_FILE_MAPPING)) {
    // 檢查必要欄位
    if (!moduleInfo.prefix) {
      validation.errors.push(`功能 ${featureName} 缺少 prefix 欄位`);
      validation.isValid = false;
    }
    
    if (!moduleInfo.description) {
      validation.warnings.push(`功能 ${featureName} 缺少 description 欄位`);
    }
    
    if (!moduleInfo.files) {
      validation.errors.push(`功能 ${featureName} 缺少 files 欄位`);
      validation.isValid = false;
    } else if (!Array.isArray(moduleInfo.files)) {
      validation.errors.push(`功能 ${featureName} 的 files 欄位必須是陣列`);
      validation.isValid = false;
    } else if (moduleInfo.files.length === 0) {
      validation.warnings.push(`功能 ${featureName} 沒有關聯的檔案`);
    }
  }
  
  return validation;
}
